import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { capturePayPalOrder } from '@/lib/paypal'
import { prisma } from '@/lib/prisma'
import { securityHeaders } from '@/lib/security'
import { sendOrderConfirmationEmail } from '@/lib/email'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const paypalOrderId = searchParams.get('token') // PayPal sends order ID as 'token'
    const payerId = searchParams.get('PayerID')

    console.log('[PayPal Success] Processing return:', { paypalOrderId, payerId })

    if (!paypalOrderId) {
      console.error('[PayPal Success] No PayPal order ID provided')
      return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=missing_order_id`)
    }

    // Get session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.error('[PayPal Success] No authenticated user')
      return NextResponse.redirect(`${process.env.SITE_URL}/auth/signin?callbackUrl=/checkout`)
    }

    // Find the order in our database
    const order = await prisma.order.findFirst({
      where: {
        userId: session.user.id,
        paypalOrderId: paypalOrderId,
        status: 'PENDING'
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        user: true
      }
    })

    if (!order) {
      console.error('[PayPal Success] Order not found:', { paypalOrderId, userId: session.user.id })
      return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=order_not_found`)
    }

    console.log('[PayPal Success] Order found:', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      status: order.status
    })

    try {
      // Capture the PayPal payment
      console.log('[PayPal Success] Capturing payment:', paypalOrderId)
      const captureResult = await capturePayPalOrder(paypalOrderId)
      
      console.log('[PayPal Success] Capture result:', {
        id: captureResult.id,
        status: captureResult.status,
        captureCount: captureResult.purchase_units?.[0]?.payments?.captures?.length
      })

      // Check if payment was successful or pending
      const capture = captureResult.purchase_units?.[0]?.payments?.captures?.[0]
      if (!capture) {
        console.error('[PayPal Success] No capture found in response:', {
          orderStatus: captureResult.status
        })
        return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=capture_failed`)
      }

      // Handle pending captures
      if (capture.status === 'PENDING') {
        console.log('[PayPal Success] Payment capture is pending:', {
          captureId: capture.id,
          reason: capture.status_details?.reason
        })

        // Update order to processing status
        await prisma.order.update({
          where: { id: order.id },
          data: {
            status: 'PROCESSING',
            paymentStatus: 'PENDING',
            paymentIntentId: capture.id,
            metadata: {
              ...order.metadata as any,
              paypalCaptureData: captureResult,
              captureStatus: 'PENDING',
              pendingReason: capture.status_details?.reason,
              pendingAt: new Date().toISOString()
            }
          }
        })

        // Redirect to success page with pending status
        return NextResponse.redirect(`${process.env.SITE_URL}/orders/success?orderId=${order.id}&status=pending`)
      } else if (capture.status !== 'COMPLETED') {
        console.error('[PayPal Success] Payment capture failed:', {
          captureStatus: capture.status,
          captureId: capture.id,
          orderStatus: captureResult.status,
          statusDetails: capture.status_details
        })
        return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=capture_failed&status=${capture.status}`)
      }

      console.log('[PayPal Success] Payment captured successfully:', {
        captureId: capture.id,
        amount: capture.amount?.value
      })

      // Check if this is a membership purchase
      const isMembershipPurchase = order.metadata &&
        typeof order.metadata === 'object' &&
        (order.metadata as any).type === 'membership'

      // Update order status
      const updatedOrder = await prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'COMPLETED',
          paymentStatus: 'COMPLETED',
          paymentIntentId: capture.id,
          metadata: {
            ...order.metadata as any,
            paypalCaptureData: captureResult,
            capturedAt: new Date().toISOString()
          }
        },
        include: isMembershipPurchase ? { user: true } : {
          items: {
            include: {
              product: true
            }
          },
          user: true
        }
      })

      if (isMembershipPurchase) {
        // Handle membership purchase
        const membershipData = updatedOrder.metadata as any
        const membershipType = membershipData.membershipType as 'BRONZE' | 'SILVER' | 'GOLD'

        const membershipDurations = {
          BRONZE: 30,
          SILVER: 90,
          GOLD: 365
        }

        const membershipLimits = {
          BRONZE: 10,
          SILVER: 25,
          GOLD: 100
        }

        const startDate = new Date()
        const endDate = new Date()
        endDate.setDate(endDate.getDate() + membershipDurations[membershipType])

        // Create or update membership
        await prisma.membership.upsert({
          where: { userId: session.user.id },
          update: {
            type: membershipType,
            status: 'ACTIVE',
            startDate,
            endDate,
            updatedAt: new Date()
          },
          create: {
            userId: session.user.id,
            type: membershipType,
            status: 'ACTIVE',
            startDate,
            endDate
          }
        })

        console.log('[PayPal Success] Membership created/updated:', {
          userId: session.user.id,
          type: membershipType,
          endDate: endDate.toISOString()
        })

        // Send membership confirmation email
        try {
          await sendOrderConfirmationEmail(updatedOrder.email, {
            orderNumber: updatedOrder.orderNumber,
            customerName: `${updatedOrder.firstName} ${updatedOrder.lastName}`,
            items: [{
              name: `${membershipType} Membership`,
              quantity: 1,
              price: Number(updatedOrder.totalAmount)
            }],
            total: Number(updatedOrder.totalAmount),
            isMembership: true,
            membershipType,
            membershipEndDate: endDate
          })
        } catch (emailError) {
          console.error('[PayPal Success] Failed to send membership confirmation email:', emailError)
        }

        // Redirect to membership success page
        return NextResponse.redirect(`${process.env.SITE_URL}/orders/success?orderId=${updatedOrder.id}&type=membership`)
      } else {
        // Create purchases for each item
        const purchases = await Promise.all(
          updatedOrder.items.map(async (item) => {
            return prisma.purchase.upsert({
              where: {
                userId_productId: {
                  userId: session.user.id,
                  productId: item.productId
                }
              },
              update: {
                orderId: updatedOrder.id,
                price: item.price,
                quantity: item.quantity,
                status: 'COMPLETED',
                updatedAt: new Date()
              },
              create: {
                userId: session.user.id,
                productId: item.productId,
                orderId: updatedOrder.id,
                price: item.price,
                quantity: item.quantity,
                status: 'COMPLETED'
              }
            })
          })
        )

        console.log('[PayPal Success] Purchases created:', purchases.length)

        // Send order confirmation email
        try {
          await sendOrderConfirmationEmail(updatedOrder.email, {
            orderNumber: updatedOrder.orderNumber,
            customerName: `${updatedOrder.firstName} ${updatedOrder.lastName}`,
            items: updatedOrder.items.map(item => ({
              name: item.product.name,
              quantity: item.quantity,
              price: Number(item.price)
            })),
            total: Number(updatedOrder.totalAmount),
            isMembership: false
          })
        } catch (emailError) {
          console.error('[PayPal Success] Failed to send order confirmation email:', emailError)
        }

        // Redirect to order success page
        return NextResponse.redirect(`${process.env.SITE_URL}/orders/success?orderId=${updatedOrder.id}`)
      }

    } catch (captureError: any) {
      console.error('[PayPal Success] Error capturing payment:', captureError)
      
      // Update order status to failed
      await prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'CANCELLED',
          paymentStatus: 'FAILED',
          metadata: {
            ...order.metadata as any,
            captureError: captureError.message,
            failedAt: new Date().toISOString()
          }
        }
      })

      return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=capture_failed&message=${encodeURIComponent(captureError.message)}`)
    }

  } catch (error: any) {
    console.error('[PayPal Success] Unexpected error:', error)
    return NextResponse.redirect(`${process.env.SITE_URL}/checkout?error=unexpected_error`)
  }
}
