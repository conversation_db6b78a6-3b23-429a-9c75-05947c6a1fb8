import { prisma } from '@/lib/prisma'

export interface MembershipPlan {
  id: string
  name: string
  type: '<PERSON><PERSON><PERSON><PERSON>' | 'SILVER' | 'GOLD'
  price: number
  duration: number // in months
  maxDailyDownloads: number
  features: string[]
  isActive: boolean
  description: string
}

// Default membership plans configuration
export const DEFAULT_MEMBERSHIP_PLANS: Record<string, MembershipPlan> = {
  BRONZE: {
    id: 'bronze',
    name: 'Bronze Plan',
    type: 'BRONZE',
    price: 299.99,
    duration: 1,
    maxDailyDownloads: 10,
    features: [
      'Download our all tools become VIP',
      '1 Month unlimited download',
      'Real value over $ 3000+',
      '500+ working forex robot and indicators',
      'Daily update new tools',
      'Includes ALL future products and updates',
      'VIP Telegram Support Group'
    ],
    description: '1 Month Unlimited Download',
    isActive: true
  },
  SILVER: {
    id: 'silver',
    name: 'Silver Plan',
    type: 'SILVER',
    price: 399.99,
    duration: 3,
    maxDailyDownloads: 25,
    features: [
      'Download our all tools become VIP',
      '3 Month unlimited download',
      'Real value over $ 30000+',
      '500+ working forex robot and indicators',
      'Daily update new tools',
      'Includes ALL future products and updates',
      'VIP Telegram Support Group'
    ],
    description: '3 Month Unlimited Download',
    isActive: true
  },
  GOLD: {
    id: 'gold',
    name: 'Gold Plan',
    type: 'GOLD',
    price: 499.99,
    duration: 12,
    maxDailyDownloads: 100,
    features: [
      'Download our all tools become VIP',
      '1 Year unlimited download',
      'Real value over $ 30000+',
      '500+ working forex robot and indicators',
      'Daily update new tools',
      'Includes ALL future products and updates',
      'VIP Telegram Support Group'
    ],
    description: '1 Year Unlimited Download',
    isActive: true
  }
}

// Get user's active membership
export async function getUserMembership(userId: string) {
  try {
    const membership = await prisma.membership.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    if (!membership) {
      return null
    }

    // Check if membership is still active
    const now = new Date()
    const isActive = membership.status === 'ACTIVE' && 
      (membership.endDate === null || membership.endDate > now)

    if (!isActive) {
      return null
    }

    return membership
  } catch (error) {
    console.error('Error fetching user membership:', error)
    return null
  }
}

// Get membership plan configuration
export function getMembershipPlan(type: 'BRONZE' | 'SILVER' | 'GOLD'): MembershipPlan {
  return DEFAULT_MEMBERSHIP_PLANS[type]
}

// Check if user can download (fair usage policy)
export async function checkDownloadLimit(userId: string): Promise<{
  canDownload: boolean
  remainingDownloads: number
  dailyLimit: number
  membershipType: string | null
  resetTime: Date
}> {
  try {
    // Get user's membership
    const membership = await getUserMembership(userId)
    
    if (!membership) {
      return {
        canDownload: false,
        remainingDownloads: 0,
        dailyLimit: 0,
        membershipType: null,
        resetTime: new Date()
      }
    }

    // Get membership plan configuration
    const plan = getMembershipPlan(membership.type)
    const dailyLimit = plan.maxDailyDownloads

    // Get today's download count
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const todayDownloads = await prisma.downloadLog.count({
      where: {
        userId,
        downloadedAt: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    })

    const remainingDownloads = Math.max(0, dailyLimit - todayDownloads)
    const canDownload = remainingDownloads > 0

    // Reset time is start of next day
    const resetTime = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    return {
      canDownload,
      remainingDownloads,
      dailyLimit,
      membershipType: membership.type,
      resetTime
    }
  } catch (error) {
    console.error('Error checking download limit:', error)
    return {
      canDownload: false,
      remainingDownloads: 0,
      dailyLimit: 0,
      membershipType: null,
      resetTime: new Date()
    }
  }
}

// Log a download
export async function logDownload(
  userId: string,
  productId: string,
  tokenId: string,
  ipAddress: string,
  userAgent: string
) {
  try {
    await prisma.downloadLog.create({
      data: {
        userId,
        productId,
        tokenId,
        ipAddress,
        userAgent
      }
    })
  } catch (error) {
    console.error('Error logging download:', error)
    throw error
  }
}

// Create or update membership
export async function createMembership(
  userId: string,
  type: 'BRONZE' | 'SILVER' | 'GOLD',
  paymentId?: string
) {
  try {
    const plan = getMembershipPlan(type)
    const startDate = new Date()
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() + plan.duration)

    // Check if user already has a membership
    const existingMembership = await prisma.membership.findUnique({
      where: { userId }
    })

    if (existingMembership) {
      // Update existing membership
      return await prisma.membership.update({
        where: { userId },
        data: {
          type,
          status: 'ACTIVE',
          startDate,
          endDate,
          autoRenew: false,
          updatedAt: new Date()
        }
      })
    } else {
      // Create new membership
      return await prisma.membership.create({
        data: {
          userId,
          type,
          status: 'ACTIVE',
          startDate,
          endDate,
          autoRenew: false
        }
      })
    }
  } catch (error) {
    console.error('Error creating membership:', error)
    throw error
  }
}

// Cancel membership
export async function cancelMembership(userId: string) {
  try {
    return await prisma.membership.update({
      where: { userId },
      data: {
        status: 'CANCELLED',
        autoRenew: false,
        updatedAt: new Date()
      }
    })
  } catch (error) {
    console.error('Error cancelling membership:', error)
    throw error
  }
}

// Get membership statistics for admin
export async function getMembershipStats() {
  try {
    const totalMembers = await prisma.membership.count()
    const activeMembers = await prisma.membership.count({
      where: {
        status: 'ACTIVE',
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      }
    })

    const membersByType = await prisma.membership.groupBy({
      by: ['type'],
      where: {
        status: 'ACTIVE',
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      },
      _count: {
        id: true
      }
    })

    const byType = {
      BRONZE: 0,
      SILVER: 0,
      GOLD: 0
    }

    membersByType.forEach(group => {
      byType[group.type] = group._count.id
    })

    // Calculate estimated monthly revenue
    const revenue = (byType.BRONZE * DEFAULT_MEMBERSHIP_PLANS.BRONZE.price) + 
                   (byType.SILVER * DEFAULT_MEMBERSHIP_PLANS.SILVER.price) + 
                   (byType.GOLD * DEFAULT_MEMBERSHIP_PLANS.GOLD.price)

    return {
      totalMembers,
      activeMembers,
      revenue: Math.round(revenue),
      byType
    }
  } catch (error) {
    console.error('Error getting membership stats:', error)
    throw error
  }
}
