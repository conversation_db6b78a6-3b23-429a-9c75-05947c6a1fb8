import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const paypalOrderId = searchParams.get('token') // PayPal sends order ID as 'token'

    console.log('[PayPal Cancel] Processing cancellation:', { paypalOrderId })

    // Get session
    const session = await getServerSession(authOptions)
    
    if (paypalOrderId && session?.user?.id) {
      // Find and update the order status
      const order = await prisma.order.findFirst({
        where: {
          userId: session.user.id,
          paypalOrderId: paypalOrderId,
          status: 'PENDING'
        }
      })

      if (order) {
        await prisma.order.update({
          where: { id: order.id },
          data: {
            status: 'CANCELLED',
            paymentStatus: 'CANCELLED',
            metadata: {
              ...order.metadata as any,
              cancelledAt: new Date().toISOString(),
              cancelledBy: 'user'
            }
          }
        })

        console.log('[PayPal Cancel] Order cancelled:', {
          orderId: order.id,
          orderNumber: order.orderNumber
        })
      }
    }

    // Redirect back to checkout with cancellation message
    return NextResponse.redirect(`${process.env.SITE_URL}/checkout?cancelled=true&message=Payment was cancelled`)

  } catch (error: any) {
    console.error('[PayPal Cancel] Error processing cancellation:', error)
    return NextResponse.redirect(`${process.env.SITE_URL}/checkout?cancelled=true`)
  }
}
