'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { XCircleIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function SubscriptionCancelPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const subscriptionId = searchParams.get('subscription_id')
  const token = searchParams.get('token')

  useEffect(() => {
    // Log the cancellation for analytics
    console.log('Subscription cancelled:', { subscriptionId, token })
  }, [subscriptionId, token])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white/5 backdrop-blur-md border border-white/10 shadow-lg rounded-lg overflow-hidden">
          {/* Header */}
          <div className="bg-red-500/10 border-b border-red-500/20 px-6 py-8 text-center">
            <XCircleIcon className="mx-auto h-16 w-16 text-red-400 mb-4" />
            <h1 className="text-3xl font-bold text-white mb-2">
              Subscription Cancelled
            </h1>
            <p className="text-lg text-gray-300">
              Your subscription setup was cancelled. No charges were made.
            </p>
          </div>

          {/* Content */}
          <div className="px-6 py-8">
            <div className="text-center space-y-6">
              <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-3">
                  What happened?
                </h3>
                <p className="text-gray-300 mb-4">
                  You cancelled the subscription setup process. No payment was processed and no subscription was created.
                </p>
                <p className="text-gray-300">
                  You can try again anytime or contact our support team if you need assistance.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/subscription"
                  className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-semibold rounded-md hover:from-yellow-500 hover:to-orange-600 transition-all duration-200"
                >
                  Try Again
                </Link>
                
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-md border border-white/20 transition-colors"
                >
                  Contact Support
                </Link>
                
                <Link
                  href="/"
                  className="inline-flex items-center justify-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-md border border-white/20 transition-colors"
                >
                  Go Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
