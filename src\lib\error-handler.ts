import { NextResponse } from 'next/server'

export interface ErrorDetails {
  code: string
  message: string
  details?: any
  stack?: string
  timestamp: string
  requestId?: string
}

export class AppError extends Error {
  public readonly code: string
  public readonly statusCode: number
  public readonly isOperational: boolean
  public readonly details?: any

  constructor(
    message: string,
    code: string = 'INTERNAL_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message)
    
    this.code = code
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.details = details
    
    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, true, details)
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTHENTICATION_ERROR', 401, true)
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403, true)
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NOT_FOUND', 404, true)
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 'CONFLICT', 409, true)
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT', 429, true)
  }
}

/**
 * Generate a unique request ID for tracking
 */
export function generateRequestId(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let id = 'REQ-'
  for (let i = 0; i < 8; i++) {
    id += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return id
}

/**
 * Log error with context
 */
export function logError(error: Error | AppError, context?: any) {
  const errorInfo: ErrorDetails = {
    code: error instanceof AppError ? error.code : 'UNKNOWN_ERROR',
    message: error.message,
    details: error instanceof AppError ? error.details : undefined,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    requestId: context?.requestId,
  }

  // In production, you might want to send this to a logging service
  console.error('Application Error:', {
    ...errorInfo,
    context,
  })

  // In development, also log to console for easier debugging
  if (process.env.NODE_ENV === 'development') {
    console.error('Error Stack:', error.stack)
    if (context) {
      console.error('Error Context:', context)
    }
  }

  return errorInfo
}

/**
 * Handle API errors and return appropriate response
 */
export function handleApiError(error: Error | AppError, requestId?: string): NextResponse {
  const errorInfo = logError(error, { requestId })

  if (error instanceof AppError) {
    return NextResponse.json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: process.env.NODE_ENV === 'development' ? error.details : undefined,
        requestId,
      }
    }, { status: error.statusCode })
  }

  // Handle unknown errors
  return NextResponse.json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'development' 
        ? error.message 
        : 'An internal error occurred',
      requestId,
    }
  }, { status: 500 })
}

/**
 * Async error wrapper for API routes
 */
export function asyncHandler(
  handler: (request: any, context?: any) => Promise<NextResponse>
) {
  return async (request: any, context?: any): Promise<NextResponse> => {
    const requestId = generateRequestId()
    
    try {
      // Add request ID to context
      const enhancedContext = { ...context, requestId }
      return await handler(request, enhancedContext)
    } catch (error) {
      return handleApiError(error as Error, requestId)
    }
  }
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(requiredVars: string[]): void {
  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    throw new AppError(
      `Missing required environment variables: ${missing.join(', ')}`,
      'ENVIRONMENT_ERROR',
      500,
      false
    )
  }
}

/**
 * Database error handler
 */
export function handleDatabaseError(error: any): AppError {
  // Prisma specific error handling
  if (error.code === 'P2002') {
    return new ConflictError('A record with this information already exists')
  }
  
  if (error.code === 'P2025') {
    return new NotFoundError('Record not found')
  }
  
  if (error.code === 'P2003') {
    return new ValidationError('Foreign key constraint failed')
  }
  
  // Generic database error
  return new AppError(
    process.env.NODE_ENV === 'development' 
      ? error.message 
      : 'Database operation failed',
    'DATABASE_ERROR',
    500,
    true,
    process.env.NODE_ENV === 'development' ? error : undefined
  )
}

/**
 * Client-side error reporter
 */
export function reportClientError(error: Error, context?: any) {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    context,
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Client Error:', errorInfo)
  }

  // In production, you might want to send this to an error tracking service
  // Example: Sentry, LogRocket, etc.
  
  return errorInfo
}

/**
 * React error boundary helper
 */
export function createErrorBoundary(fallbackComponent: React.ComponentType<any>) {
  return class ErrorBoundary extends React.Component {
    constructor(props: any) {
      super(props)
      this.state = { hasError: false, error: null }
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: any) {
      reportClientError(error, errorInfo)
    }

    render() {
      if (this.state.hasError) {
        return React.createElement(fallbackComponent, { 
          error: this.state.error 
        })
      }

      return this.props.children
    }
  }
}
