import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('Adding PayPal support to the database...')

    // The schema changes will be handled by Prisma migration
    // This script is for any data migrations if needed

    console.log('PayPal support added successfully!')
  } catch (error) {
    console.error('Error adding PayPal support:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
