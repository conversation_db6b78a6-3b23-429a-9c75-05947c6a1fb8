'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { 
  XMarkIcon, 
  DocumentTextIcon, 
  CreditCardIcon, 
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import Image from 'next/image'

interface OrderItem {
  id: string
  quantity: number
  price: number
  product: {
    id: string
    name: string
    slug: string
    images: string[]
  }
}

interface Order {
  id: string
  orderNumber: string
  status: string
  paymentStatus: string
  totalAmount: number
  subtotalAmount: number
  discountAmount?: number
  paymentMethod?: string
  createdAt: string
  updatedAt: string
  items: OrderItem[]
  firstName?: string
  lastName?: string
  email?: string
  notes?: string
  adminNotes?: string
}

interface OrderDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  order: Order | null
}

export function OrderDetailsModal({ isOpen, onClose, order }: OrderDetailsModalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted || !isOpen || !order) return null

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return 'text-green-400 bg-green-400/10 border-green-400/20'
      case 'PENDING':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20'
      case 'CANCELLED':
        return 'text-red-400 bg-red-400/10 border-red-400/20'
      case 'PROCESSING':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20'
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return <CheckCircleIcon className="w-4 h-4" />
      case 'PENDING':
        return <ClockIcon className="w-4 h-4" />
      case 'CANCELLED':
        return <XCircleIcon className="w-4 h-4" />
      case 'PROCESSING':
        return <DocumentTextIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'COMPLETED':
      case 'PAID':
        return 'text-green-400 bg-green-400/10 border-green-400/20'
      case 'PENDING':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20'
      case 'FAILED':
        return 'text-red-400 bg-red-400/10 border-red-400/20'
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20'
    }
  }

  return createPortal(
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
          onClick={onClose}
        />
        
        <div className="relative bg-gray-900 border border-white/10 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div>
              <h2 className="text-xl font-semibold text-white">Order Details</h2>
              <p className="text-gray-400 text-sm">#{order.orderNumber}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <XMarkIcon className="h-5 w-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Order Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-300">Order Status</span>
                </div>
                <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                  {getStatusIcon(order.status)}
                  <span className="capitalize">{order.status.toLowerCase()}</span>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-300">Payment Status</span>
                </div>
                <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${getPaymentStatusColor(order.paymentStatus)}`}>
                  <span className="capitalize">{order.paymentStatus?.toLowerCase() || 'Pending'}</span>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <ShoppingBagIcon className="h-5 w-5 text-gray-400" />
                <h3 className="text-lg font-medium text-white">Order Items</h3>
              </div>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-3 bg-white/5 rounded-lg">
                    {/* Product Image */}
                    <div className="w-16 h-16 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                      {item.product.images && item.product.images.length > 0 ? (
                        <Image
                          src={item.product.images[0]}
                          alt={item.product.name}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <DocumentTextIcon className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium truncate">
                        {item.product.name}
                      </h4>
                      <p className="text-sm text-gray-400">
                        Quantity: {item.quantity}
                      </p>
                    </div>

                    {/* Price */}
                    <div className="text-right">
                      <p className="text-white font-medium">${item.price.toFixed(2)}</p>
                      <p className="text-gray-400 text-sm">each</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                <h3 className="text-lg font-medium text-white">Order Summary</h3>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-gray-300">
                  <span>Subtotal:</span>
                  <span>${order.subtotalAmount.toFixed(2)}</span>
                </div>
                {order.discountAmount && order.discountAmount > 0 && (
                  <div className="flex justify-between text-green-400">
                    <span>Discount:</span>
                    <span>-${order.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="border-t border-white/10 pt-2 flex justify-between text-white font-semibold text-lg">
                  <span>Total:</span>
                  <span>${order.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <CalendarIcon className="h-5 w-5 text-gray-400" />
                <h3 className="text-lg font-medium text-white">Order Timeline</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-300">Order Placed</label>
                  <p className="text-white">{new Date(order.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-300">Last Updated</label>
                  <p className="text-white">{new Date(order.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Notes */}
            {(order.notes || order.adminNotes) && (
              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-4">Notes</h3>
                {order.notes && (
                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-300">Your Notes</label>
                    <p className="text-white text-sm">{order.notes}</p>
                  </div>
                )}
                {order.adminNotes && (
                  <div>
                    <label className="text-sm font-medium text-gray-300">Admin Notes</label>
                    <p className="text-white text-sm">{order.adminNotes}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 p-6 border-t border-white/10">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  )
}
