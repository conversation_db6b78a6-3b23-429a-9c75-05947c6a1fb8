import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getPayPalSubscription, mapPayPalSubscriptionStatus } from '@/lib/paypal-subscriptions'
import { prisma } from '@/lib/prisma'
import { sendOrderConfirmationEmail } from '@/lib/email'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('subscription_id')
    const baToken = searchParams.get('ba_token') // Billing agreement token

    console.log('[Subscription Success] Processing PayPal return:', { 
      subscriptionId, 
      baToken 
    })

    if (!subscriptionId) {
      console.error('[Subscription Success] No subscription ID provided')
      return NextResponse.redirect(`${process.env.SITE_URL}/membership?error=missing_subscription_id`)
    }

    // Get session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.error('[Subscription Success] No authenticated user')
      return NextResponse.redirect(`${process.env.SITE_URL}/auth/signin?callbackUrl=/membership`)
    }

    try {
      // Get PayPal subscription details
      console.log('[Subscription Success] Getting PayPal subscription details:', subscriptionId)
      const paypalSubscription = await getPayPalSubscription(subscriptionId)
      
      console.log('[Subscription Success] PayPal subscription status:', {
        id: paypalSubscription.id,
        status: paypalSubscription.status,
        nextBillingTime: paypalSubscription.billing_info?.next_billing_time
      })

      // Find our subscription record
      const subscription = await prisma.subscription.findFirst({
        where: {
          userId: session.user.id,
          paypalSubscriptionId: subscriptionId
        },
        include: {
          user: true
        }
      })

      if (!subscription) {
        console.error('[Subscription Success] Subscription not found:', { 
          subscriptionId, 
          userId: session.user.id 
        })
        return NextResponse.redirect(`${process.env.SITE_URL}/membership?error=subscription_not_found`)
      }

      // Map PayPal status to our internal status
      const status = mapPayPalSubscriptionStatus(paypalSubscription.status)
      
      // Calculate period dates
      const currentPeriodStart = new Date(paypalSubscription.start_time)
      const nextBillingTime = paypalSubscription.billing_info?.next_billing_time
      const currentPeriodEnd = nextBillingTime ? new Date(nextBillingTime) : subscription.currentPeriodEnd

      console.log('[Subscription Success] Updating subscription:', {
        id: subscription.id,
        status,
        currentPeriodStart,
        currentPeriodEnd
      })

      // Update subscription with PayPal data
      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status,
          currentPeriodStart,
          currentPeriodEnd,
          nextBillingDate: nextBillingTime ? new Date(nextBillingTime) : null,
          metadata: {
            paypalSubscriptionData: paypalSubscription,
            activatedAt: new Date().toISOString(),
            baToken
          },
          updatedAt: new Date()
        },
        include: {
          user: true
        }
      })

      // If subscription is now active, create initial payment record
      if (status === 'ACTIVE') {
        const billingPeriod = new Date().toISOString().slice(0, 7) // YYYY-MM format
        
        await prisma.subscriptionPayment.create({
          data: {
            subscriptionId: subscription.id,
            amount: getSubscriptionPrice(subscription.planType),
            currency: 'USD',
            status: 'COMPLETED',
            billingPeriod,
            paidAt: new Date(),
            metadata: {
              paypalSubscriptionData: paypalSubscription,
              initialPayment: true
            }
          }
        })

        console.log('[Subscription Success] Initial payment record created')

        // Send confirmation email
        try {
          await sendSubscriptionConfirmationEmail(updatedSubscription)
        } catch (emailError) {
          console.error('[Subscription Success] Failed to send confirmation email:', emailError)
          // Don't fail the whole process for email errors
        }
      }

      console.log('[Subscription Success] Subscription activated successfully:', {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planType: updatedSubscription.planType
      })

      // Redirect to success page
      return NextResponse.redirect(`${process.env.SITE_URL}/membership/success?subscription_id=${subscription.id}`)

    } catch (paypalError: any) {
      console.error('[Subscription Success] PayPal API error:', paypalError)
      
      // Update subscription status to failed
      await prisma.subscription.updateMany({
        where: {
          userId: session.user.id,
          paypalSubscriptionId: subscriptionId
        },
        data: {
          status: 'SUSPENDED',
          metadata: {
            error: paypalError.message,
            failedAt: new Date().toISOString()
          },
          updatedAt: new Date()
        }
      })

      return NextResponse.redirect(`${process.env.SITE_URL}/membership?error=activation_failed&message=${encodeURIComponent(paypalError.message)}`)
    }

  } catch (error: any) {
    console.error('[Subscription Success] Unexpected error:', error)
    return NextResponse.redirect(`${process.env.SITE_URL}/membership?error=unexpected_error`)
  }
}

// Helper function to get subscription price
function getSubscriptionPrice(planType: 'BRONZE' | 'SILVER' | 'GOLD'): number {
  const prices = {
    BRONZE: 29.99,
    SILVER: 39.99,
    GOLD: 49.99
  }
  return prices[planType]
}

// Helper function to send subscription confirmation email
async function sendSubscriptionConfirmationEmail(subscription: any) {
  try {
    const planNames = {
      BRONZE: 'Bronze Plan',
      SILVER: 'Silver Plan',
      GOLD: 'Gold Plan'
    }

    await sendOrderConfirmationEmail(subscription.user.email, {
      orderNumber: `SUB-${subscription.id.slice(-8).toUpperCase()}`,
      customerName: `${subscription.user.firstName} ${subscription.user.lastName}`,
      items: [{
        name: `${planNames[subscription.planType]} Subscription`,
        quantity: 1,
        price: getSubscriptionPrice(subscription.planType)
      }],
      total: getSubscriptionPrice(subscription.planType),
      isSubscription: true,
      subscriptionType: subscription.planType,
      nextBillingDate: subscription.nextBillingDate,
      currentPeriodEnd: subscription.currentPeriodEnd
    })

    console.log('[Subscription Success] Confirmation email sent to:', subscription.user.email)
  } catch (error) {
    console.error('[Subscription Success] Error sending confirmation email:', error)
    throw error
  }
}
