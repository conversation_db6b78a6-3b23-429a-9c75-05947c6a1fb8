# PayPal Integration Documentation

## Overview
This document outlines the complete PayPal integration implemented in the Forex Bot Zone application, following PayPal's Standard Checkout documentation.

## Features Implemented

### 1. PayPal Standard Checkout
- ✅ PayPal JavaScript SDK integration
- ✅ PayPal Buttons component
- ✅ Order creation and capture flow
- ✅ Webhook handling for payment events
- ✅ Admin configuration panel

### 2. Payment Flow
1. **Order Creation**: Customer selects PayPal and creates order
2. **PayPal Approval**: Customer approves payment on PayPal
3. **Payment Capture**: Server captures the approved payment
4. **Order Completion**: Order status updated and purchases created
5. **Email Confirmation**: Customer receives order confirmation

### 3. Security Features
- ✅ Rate limiting on PayPal API endpoints
- ✅ Webhook signature verification
- ✅ User authorization checks
- ✅ Input validation and sanitization
- ✅ Secure error handling

## File Structure

### Backend Components
```
src/lib/paypal.ts                     # PayPal SDK configuration and utilities
src/app/api/paypal/
├── config/route.ts                   # PayPal client configuration
├── create-order/route.ts             # Create PayPal order
├── capture-order/route.ts            # Capture PayPal payment
└── webhook/route.ts                  # Handle PayPal webhooks
```

### Frontend Components
```
src/components/payments/
└── paypal-payment-form.tsx           # PayPal payment form component
```

### Admin Components
```
src/components/admin/
└── payment-methods-settings.tsx      # PayPal admin configuration
```

### Database Schema
```
prisma/schema.prisma                  # Updated with PayPal fields
```

## Environment Variables

### Required Variables
```env
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id
```

### Environment Detection
- **Development**: Uses PayPal Sandbox (api-m.sandbox.paypal.com)
- **Production**: Uses PayPal Live (api-m.paypal.com)

## API Endpoints

### 1. GET /api/paypal/config
Returns PayPal client ID for frontend SDK initialization.

### 2. POST /api/paypal/create-order
Creates a PayPal order with the following payload:
```json
{
  "items": [
    {
      "productId": "string",
      "quantity": number
    }
  ],
  "customerInfo": {
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  },
  "couponCode": "string" // optional
}
```

### 3. POST /api/paypal/capture-order
Captures a PayPal payment:
```json
{
  "paypalOrderId": "string"
}
```

### 4. POST /api/paypal/webhook
Handles PayPal webhook events:
- `CHECKOUT.ORDER.APPROVED`
- `PAYMENT.CAPTURE.COMPLETED`
- `PAYMENT.CAPTURE.DENIED`
- `PAYMENT.CAPTURE.DECLINED`

## Database Schema Changes

### Order Model Updates
```prisma
model Order {
  // ... existing fields
  paypalOrderId   String?  // PayPal order ID
  metadata        Json?    // PayPal payment data
  // ... rest of fields
}
```

### Payment Method Type
```prisma
enum PaymentMethodType {
  STRIPE
  PAYPAL  // Added
  MANUAL
}
```

## Frontend Integration

### PayPal SDK Loading
The PayPal JavaScript SDK is dynamically loaded with the following configuration:
- Client ID from environment
- Currency: USD
- Intent: capture
- Components: buttons

### PayPal Buttons Configuration
```javascript
{
  createOrder: async () => { /* Create order via API */ },
  onApprove: async (data) => { /* Capture payment */ },
  onError: (err) => { /* Handle errors */ },
  onCancel: (data) => { /* Handle cancellation */ },
  style: {
    layout: 'vertical',
    color: 'gold',
    shape: 'rect',
    label: 'pay',
    height: 45
  }
}
```

## Admin Configuration

### PayPal Status Panel
The admin panel displays:
- ✅ PayPal configuration status
- ✅ Environment (Sandbox/Production)
- ✅ Integration checklist
- ✅ Client ID status (masked for security)

### Payment Methods Management
- PayPal appears as a payment method type
- Color-coded status indicators
- Integration status monitoring

## Webhook Configuration

### Required Webhook Events
Configure these events in your PayPal Developer Dashboard:
1. `CHECKOUT.ORDER.APPROVED`
2. `PAYMENT.CAPTURE.COMPLETED`
3. `PAYMENT.CAPTURE.DENIED`
4. `PAYMENT.CAPTURE.DECLINED`

### Webhook URL
```
https://yourdomain.com/api/paypal/webhook
```

## Testing

### Sandbox Testing
1. Use PayPal Sandbox credentials
2. Test with PayPal test accounts
3. Verify webhook delivery in PayPal Developer Dashboard

### Test Scenarios
- ✅ Successful payment flow
- ✅ Payment cancellation
- ✅ Payment failure handling
- ✅ Webhook event processing
- ✅ Order status updates

## Security Considerations

### Implemented Security Measures
1. **Rate Limiting**: 10 PayPal orders per 15 minutes per IP
2. **Webhook Verification**: PayPal signature validation
3. **Input Validation**: Zod schema validation
4. **Authorization**: User ownership verification
5. **Error Handling**: Safe error responses
6. **HTTPS Required**: PayPal requires HTTPS in production

### Best Practices Followed
- Never expose PayPal Client Secret to frontend
- Validate all webhook signatures
- Use server-side order capture
- Implement proper error handling
- Log security events

## Troubleshooting

### Common Issues
1. **PayPal SDK not loading**: Check client ID configuration
2. **Webhook not working**: Verify webhook URL and signature
3. **Orders not capturing**: Check PayPal order status
4. **Environment mismatch**: Ensure correct sandbox/live settings

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify PayPal credentials in environment
3. Test webhook delivery in PayPal Dashboard
4. Check server logs for API errors
5. Validate order status in database

## Production Deployment

### Pre-deployment Checklist
- [ ] PayPal Live credentials configured
- [ ] Webhook URL updated to production
- [ ] HTTPS certificate installed
- [ ] Database migration applied
- [ ] PayPal app reviewed and approved

### Go-Live Steps
1. Update environment variables to production
2. Configure PayPal Live webhooks
3. Test with small transaction
4. Monitor logs and webhook delivery
5. Verify order completion flow

## Support and Maintenance

### Monitoring
- Monitor PayPal webhook delivery
- Track payment success rates
- Log PayPal API errors
- Monitor order completion rates

### Regular Tasks
- Review PayPal transaction logs
- Update PayPal SDK if needed
- Monitor webhook event processing
- Verify payment reconciliation

## Additional Resources

- [PayPal Developer Documentation](https://developer.paypal.com/docs/checkout/standard/)
- [PayPal JavaScript SDK Reference](https://developer.paypal.com/sdk/js/)
- [PayPal Webhook Events](https://developer.paypal.com/api/rest/webhooks/)
- [PayPal Testing Guide](https://developer.paypal.com/tools/sandbox/)

## Contact Information

For PayPal integration support:
- PayPal Developer Support: https://developer.paypal.com/support/
- PayPal Technical Documentation: https://developer.paypal.com/docs/
