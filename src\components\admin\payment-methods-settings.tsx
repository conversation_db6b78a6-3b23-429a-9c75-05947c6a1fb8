'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface PaymentMethod {
  id: string
  name: string
  type: 'STRIPE' | 'MANUAL'
  currency: string
  walletAddress?: string
  qrCodeUrl?: string
  discountPercentage: number
  isActive: boolean
  instructions?: string
  minimumAmount?: number
  maximumAmount?: number
  processingTime?: string
  sortOrder: number
  createdAt: string
  updatedAt: string
}

export function PaymentMethodsSettings() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(true)
  const [paypalConfig, setPaypalConfig] = useState<{
    configured: boolean
    environment: string
    clientIdConfigured: boolean
    clientSecretConfigured: boolean
  } | null>(null)
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    type: 'MANUAL' as 'STRIPE' | 'PAYPAL' | 'MANUAL',
    currency: 'USDT',
    walletAddress: '',
    qrCodeUrl: '',
    discountPercentage: 0,
    isActive: true,
    instructions: '',
    minimumAmount: '',
    maximumAmount: '',
    processingTime: '',
    sortOrder: 0
  })

  useEffect(() => {
    fetchPaymentMethods()
    fetchPaypalConfig()
  }, [])

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/payment-methods')
      const data = await response.json()

      if (data.success) {
        setPaymentMethods(data.data)
      } else {
        toast.error('Failed to fetch payment methods')
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error)
      toast.error('Failed to fetch payment methods')
    } finally {
      setLoading(false)
    }
  }

  const fetchPaypalConfig = async () => {
    try {
      const response = await fetch('/api/admin/paypal-status')
      const data = await response.json()

      if (data.success) {
        setPaypalConfig(data.data)
      }
    } catch (error) {
      console.error('Error fetching PayPal config:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? (value === '' ? '' : Number(value)) : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const submitData = {
        ...formData,
        minimumAmount: formData.minimumAmount === '' ? null : Number(formData.minimumAmount),
        maximumAmount: formData.maximumAmount === '' ? null : Number(formData.maximumAmount),
      }

      const url = editingMethod 
        ? `/api/admin/payment-methods/${editingMethod.id}`
        : '/api/admin/payment-methods'
      
      const method = editingMethod ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      })

      const data = await response.json()

      if (data.success) {
        toast.success(editingMethod ? 'Payment method updated!' : 'Payment method created!')
        setIsModalOpen(false)
        resetForm()
        fetchPaymentMethods()
      } else {
        toast.error(data.message || 'Failed to save payment method')
      }
    } catch (error) {
      console.error('Error saving payment method:', error)
      toast.error('Failed to save payment method')
    }
  }

  const handleEdit = (method: PaymentMethod) => {
    setEditingMethod(method)
    setFormData({
      name: method.name,
      type: method.type,
      currency: method.currency,
      walletAddress: method.walletAddress || '',
      qrCodeUrl: method.qrCodeUrl || '',
      discountPercentage: method.discountPercentage,
      isActive: method.isActive,
      instructions: method.instructions || '',
      minimumAmount: method.minimumAmount?.toString() || '',
      maximumAmount: method.maximumAmount?.toString() || '',
      processingTime: method.processingTime || '',
      sortOrder: method.sortOrder
    })
    setIsModalOpen(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment method?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/payment-methods/${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Payment method deleted!')
        fetchPaymentMethods()
      } else {
        toast.error(data.message || 'Failed to delete payment method')
      }
    } catch (error) {
      console.error('Error deleting payment method:', error)
      toast.error('Failed to delete payment method')
    }
  }

  const toggleActive = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/payment-methods/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Payment method ${!isActive ? 'enabled' : 'disabled'}!`)
        fetchPaymentMethods()
      } else {
        toast.error(data.message || 'Failed to update payment method')
      }
    } catch (error) {
      console.error('Error updating payment method:', error)
      toast.error('Failed to update payment method')
    }
  }

  const resetForm = () => {
    setEditingMethod(null)
    setFormData({
      name: '',
      type: 'MANUAL',
      currency: 'USDT',
      walletAddress: '',
      qrCodeUrl: '',
      discountPercentage: 0,
      isActive: true,
      instructions: '',
      minimumAmount: '',
      maximumAmount: '',
      processingTime: '',
      sortOrder: 0
    })
  }

  const handleNewMethod = () => {
    resetForm()
    setIsModalOpen(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Payment Methods</h2>
          <p className="text-gray-400">Manage payment methods and their settings</p>
        </div>
        <Button onClick={handleNewMethod} className="bg-yellow-500 hover:bg-yellow-600 text-black">
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Payment Method
        </Button>
      </div>

      {/* PayPal Settings */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-white">PayPal Configuration</h3>
            <p className="text-gray-400 text-sm">Configure PayPal payment gateway settings</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">PayPal Status:</span>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              paypalConfig?.configured ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {paypalConfig?.configured ? 'Configured' : 'Not Configured'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              PayPal Client ID
            </label>
            <input
              type="text"
              value={paypalConfig?.clientIdConfigured ? '••••••••••••••••' : 'Not configured'}
              disabled
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-300"
            />
            <p className="text-xs text-gray-400 mt-1">
              Configure in environment variables (PAYPAL_CLIENT_ID)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              PayPal Environment
            </label>
            <input
              type="text"
              value={paypalConfig?.environment || 'Loading...'}
              disabled
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-300"
            />
            <p className="text-xs text-gray-400 mt-1">
              Automatically set based on NODE_ENV
            </p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-700/50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-300 mb-2">PayPal Integration Status</h4>
          <ul className="text-xs text-gray-300 space-y-1">
            <li className="flex items-center">
              <span className={`w-2 h-2 rounded-full mr-2 ${
                paypalConfig?.clientIdConfigured ? 'bg-green-400' : 'bg-red-400'
              }`}></span>
              Client ID: {paypalConfig?.clientIdConfigured ? 'Configured' : 'Missing'}
            </li>
            <li className="flex items-center">
              <span className={`w-2 h-2 rounded-full mr-2 ${
                paypalConfig?.clientSecretConfigured ? 'bg-green-400' : 'bg-red-400'
              }`}></span>
              Client Secret: {paypalConfig?.clientSecretConfigured ? 'Configured' : 'Missing'}
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 rounded-full mr-2 bg-green-400"></span>
              PayPal SDK: Integrated
            </li>
          </ul>
        </div>
      </div>

      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-800/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Discount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {paymentMethods.map((method) => (
                <tr key={method.id} className="hover:bg-white/5">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-white">{method.name}</div>
                      {method.walletAddress && (
                        <div className="text-xs text-gray-400 truncate max-w-xs">
                          {method.walletAddress}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      method.type === 'STRIPE'
                        ? 'bg-blue-100 text-blue-800'
                        : method.type === 'PAYPAL'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {method.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {method.currency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {method.discountPercentage}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      method.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {method.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleActive(method.id, method.isActive)}
                        className={method.isActive ? "text-red-400 hover:text-red-300" : "text-green-400 hover:text-green-300"}
                      >
                        {method.isActive ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(method)}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(method.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {paymentMethods.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400">No payment methods found.</p>
          </div>
        )}
      </div>

      {/* Payment Method Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
              className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
              onClick={() => setIsModalOpen(false)}
            />

            <div className="relative transform overflow-hidden rounded-lg bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
              <form onSubmit={handleSubmit}>
                <div className="bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-medium leading-6 text-white">
                      {editingMethod ? 'Edit Payment Method' : 'Add Payment Method'}
                    </h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-white">Name</Label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="mt-1 bg-gray-700 border-gray-600 text-white"
                        placeholder="e.g., USDT (TRC20)"
                      />
                    </div>

                    <div>
                      <Label htmlFor="type" className="text-white">Type</Label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className="mt-1 w-full bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="MANUAL">Manual</option>
                        <option value="STRIPE">Stripe</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="currency" className="text-white">Currency</Label>
                      <Input
                        id="currency"
                        name="currency"
                        type="text"
                        value={formData.currency}
                        onChange={handleInputChange}
                        required
                        className="mt-1 bg-gray-700 border-gray-600 text-white"
                        placeholder="e.g., USDT, BTC, ETH"
                      />
                    </div>

                    <div>
                      <Label htmlFor="discountPercentage" className="text-white">Discount %</Label>
                      <Input
                        id="discountPercentage"
                        name="discountPercentage"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={formData.discountPercentage}
                        onChange={handleInputChange}
                        className="mt-1 bg-gray-700 border-gray-600 text-white"
                        placeholder="0"
                      />
                    </div>

                    {formData.type === 'MANUAL' && (
                      <>
                        <div className="md:col-span-2">
                          <Label htmlFor="walletAddress" className="text-white">Wallet Address</Label>
                          <Input
                            id="walletAddress"
                            name="walletAddress"
                            type="text"
                            value={formData.walletAddress}
                            onChange={handleInputChange}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="Enter wallet address"
                          />
                        </div>

                        <div>
                          <Label htmlFor="minimumAmount" className="text-white">Minimum Amount</Label>
                          <Input
                            id="minimumAmount"
                            name="minimumAmount"
                            type="number"
                            min="0"
                            step="0.01"
                            value={formData.minimumAmount}
                            onChange={handleInputChange}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="Optional"
                          />
                        </div>

                        <div>
                          <Label htmlFor="maximumAmount" className="text-white">Maximum Amount</Label>
                          <Input
                            id="maximumAmount"
                            name="maximumAmount"
                            type="number"
                            min="0"
                            step="0.01"
                            value={formData.maximumAmount}
                            onChange={handleInputChange}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="Optional"
                          />
                        </div>

                        <div>
                          <Label htmlFor="processingTime" className="text-white">Processing Time</Label>
                          <Input
                            id="processingTime"
                            name="processingTime"
                            type="text"
                            value={formData.processingTime}
                            onChange={handleInputChange}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="e.g., 1-24 hours"
                          />
                        </div>

                        <div>
                          <Label htmlFor="sortOrder" className="text-white">Sort Order</Label>
                          <Input
                            id="sortOrder"
                            name="sortOrder"
                            type="number"
                            min="0"
                            value={formData.sortOrder}
                            onChange={handleInputChange}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="0"
                          />
                        </div>

                        <div className="md:col-span-2">
                          <Label htmlFor="instructions" className="text-white">Instructions</Label>
                          <Textarea
                            id="instructions"
                            name="instructions"
                            value={formData.instructions}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 bg-gray-700 border-gray-600 text-white"
                            placeholder="Payment instructions for users"
                          />
                        </div>
                      </>
                    )}

                    <div className="md:col-span-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="isActive"
                          checked={formData.isActive}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-yellow-400 bg-gray-700 border-gray-600 rounded focus:ring-yellow-400"
                        />
                        <span className="ml-2 text-white">Active</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <Button
                    type="submit"
                    className="w-full justify-center bg-yellow-500 hover:bg-yellow-600 text-black sm:ml-3 sm:w-auto"
                  >
                    {editingMethod ? 'Update' : 'Create'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className="mt-3 w-full justify-center sm:mt-0 sm:w-auto"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
