/**
 * @deprecated This admin membership API is deprecated in favor of the new subscription system.
 * This endpoint now returns subscription plan data for compatibility.
 * This endpoint will be removed in a future version.
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createPlanSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.enum(['BRONZE', 'SILVER', 'GOLD']),
  price: z.number().min(0, 'Price must be positive'),
  duration: z.number().min(1, 'Duration must be at least 1 month'),
  maxDailyDownloads: z.number().min(1, 'Max daily downloads must be at least 1'),
  features: z.array(z.string()).min(1, 'At least one feature is required'),
  description: z.string().min(1, 'Description is required'),
  isActive: z.boolean().default(true)
})

// GET /api/admin/memberships/plans - Get all membership plans
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Return subscription plans data to match the new subscription system
    const plans = [
      {
        id: '1',
        name: 'Bronze Plan',
        type: 'BRONZE',
        price: 29.99,
        duration: 1,
        maxDailyDownloads: 10,
        features: [
          'Access to all forex tools and EAs',
          '10 downloads per day',
          'Monthly billing',
          'Basic customer support',
          'VIP Telegram group access',
          'Regular updates and new tools'
        ],
        description: '10 downloads per day',
        isActive: true
      },
      {
        id: '2',
        name: 'Silver Plan',
        type: 'SILVER',
        price: 39.99,
        duration: 1,
        maxDailyDownloads: 25,
        features: [
          'Everything in Bronze Plan',
          '25 downloads per day',
          'Priority customer support',
          'Early access to new tools',
          'Advanced trading strategies',
          'Custom indicator requests'
        ],
        description: '25 downloads per day',
        isActive: false // Not configured yet
      },
      {
        id: '3',
        name: 'Gold Plan',
        type: 'GOLD',
        price: 49.99,
        duration: 1,
        maxDailyDownloads: 100,
        features: [
          'Everything in Silver Plan',
          '100 downloads per day',
          'Dedicated account manager',
          '1-on-1 trading consultation',
          'Custom EA development',
          'Backtesting services'
        ],
        description: '100 downloads per day',
        isActive: false // Not configured yet
      }
    ]

    return NextResponse.json({ success: true, plans })
  } catch (error) {
    console.error('Error fetching membership plans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch membership plans' },
      { status: 500 }
    )
  }
}

// POST /api/admin/memberships/plans - Create new membership plan
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPlanSchema.parse(body)

    // For now, return success response
    // In a real implementation, you would create in database
    const newPlan = {
      id: Date.now().toString(),
      ...validatedData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    return NextResponse.json({ 
      success: true, 
      plan: newPlan,
      message: 'Membership plan created successfully' 
    })
  } catch (error) {
    console.error('Error creating membership plan:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create membership plan' },
      { status: 500 }
    )
  }
}
