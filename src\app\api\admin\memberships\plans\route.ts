import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createPlanSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.enum(['BRONZE', 'SILVER', 'GOLD']),
  price: z.number().min(0, 'Price must be positive'),
  duration: z.number().min(1, 'Duration must be at least 1 month'),
  maxDailyDownloads: z.number().min(1, 'Max daily downloads must be at least 1'),
  features: z.array(z.string()).min(1, 'At least one feature is required'),
  description: z.string().min(1, 'Description is required'),
  isActive: z.boolean().default(true)
})

// GET /api/admin/memberships/plans - Get all membership plans
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // For now, return mock data since we don't have a MembershipPlan model yet
    // In a real implementation, you would fetch from database
    const plans = [
      {
        id: '1',
        name: 'Bronze Plan',
        type: 'BRONZE',
        price: 299.99,
        duration: 1,
        maxDailyDownloads: 10,
        features: [
          'Download our all tools become VIP',
          '1 Month unlimited download',
          'Real value over $ 3000+',
          '500+ working forex robot and indicators',
          'Daily update new tools',
          'Includes ALL future products and updates',
          'VIP Telegram Support Group'
        ],
        description: '1 Month Unlimited Download',
        isActive: true
      },
      {
        id: '2',
        name: 'Silver Plan',
        type: 'SILVER',
        price: 399.99,
        duration: 3,
        maxDailyDownloads: 25,
        features: [
          'Download our all tools become VIP',
          '3 Month unlimited download',
          'Real value over $ 30000+',
          '500+ working forex robot and indicators',
          'Daily update new tools',
          'Includes ALL future products and updates',
          'VIP Telegram Support Group'
        ],
        description: '3 Month Unlimited Download',
        isActive: true
      },
      {
        id: '3',
        name: 'Gold Plan',
        type: 'GOLD',
        price: 499.99,
        duration: 12,
        maxDailyDownloads: 100,
        features: [
          'Download our all tools become VIP',
          '1 Year unlimited download',
          'Real value over $ 30000+',
          '500+ working forex robot and indicators',
          'Daily update new tools',
          'Includes ALL future products and updates',
          'VIP Telegram Support Group'
        ],
        description: '1 Year Unlimited Download',
        isActive: true
      }
    ]

    return NextResponse.json({ success: true, plans })
  } catch (error) {
    console.error('Error fetching membership plans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch membership plans' },
      { status: 500 }
    )
  }
}

// POST /api/admin/memberships/plans - Create new membership plan
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPlanSchema.parse(body)

    // For now, return success response
    // In a real implementation, you would create in database
    const newPlan = {
      id: Date.now().toString(),
      ...validatedData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    return NextResponse.json({ 
      success: true, 
      plan: newPlan,
      message: 'Membership plan created successfully' 
    })
  } catch (error) {
    console.error('Error creating membership plan:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create membership plan' },
      { status: 500 }
    )
  }
}
