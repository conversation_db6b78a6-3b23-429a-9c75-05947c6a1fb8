'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import { 
  CrownIcon, 
  CalendarIcon, 
  ArrowDownTrayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface Membership {
  id: string
  type: 'BRONZE' | 'SILVER' | 'GOLD'
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED'
  startDate: string
  endDate: string | null
  downloadLimit: number
  downloadCount: number
  lastResetDate: string
}

interface MembershipHistory {
  id: string
  type: 'BRONZE' | 'SILVER' | 'GOLD'
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED'
  startDate: string
  endDate: string | null
  paymentMethod: string
  createdAt: string
}

const membershipColors = {
  BRONZE: 'from-amber-600 to-amber-800',
  SILVER: 'from-gray-400 to-gray-600', 
  GOLD: 'from-yellow-400 to-yellow-600'
}

const membershipBadgeColors = {
  BRONZE: 'bg-amber-500/20 text-amber-400 border-amber-500/30',
  SILVER: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
  GOLD: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
}

export default function MembershipDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [membership, setMembership] = useState<Membership | null>(null)
  const [membershipHistory, setMembershipHistory] = useState<MembershipHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/dashboard/membership')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.id) {
      fetchMembershipData()
    }
  }, [session])

  const fetchMembershipData = async () => {
    try {
      setIsLoading(true)
      
      // Fetch current membership
      const membershipResponse = await fetch('/api/memberships/status')
      const membershipData = await membershipResponse.json()
      
      if (membershipData.success) {
        setMembership(membershipData.membership)
      }

      // Fetch membership history
      const historyResponse = await fetch('/api/memberships/history')
      const historyData = await historyResponse.json()
      
      if (historyData.success) {
        setMembershipHistory(historyData.history)
      }
    } catch (error) {
      console.error('Error fetching membership data:', error)
      toast.error('Failed to load membership data')
    } finally {
      setIsLoading(false)
    }
  }

  const getDaysRemaining = (endDate: string | null) => {
    if (!endDate) return null
    const end = new Date(endDate)
    const now = new Date()
    const diffTime = end.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays > 0 ? diffDays : 0
  }

  const getDownloadPercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100)
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Membership Dashboard</h1>
          <p className="text-gray-400">Manage your membership and track your usage</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Current Membership */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-900/50 border-gray-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <CrownIcon className="w-6 h-6 mr-2 text-yellow-400" />
                  Current Membership
                </CardTitle>
              </CardHeader>
              <CardContent>
                {membership ? (
                  <div className="space-y-6">
                    {/* Membership Status */}
                    <div className={`bg-gradient-to-r ${membershipColors[membership.type]} p-6 rounded-xl text-white`}>
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-2xl font-bold">{membership.type} MEMBERSHIP</h3>
                          <Badge className={`mt-2 ${membershipBadgeColors[membership.type]}`}>
                            {membership.status}
                          </Badge>
                        </div>
                        <CrownIcon className="w-12 h-12 opacity-50" />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="opacity-75">Started</p>
                          <p className="font-semibold">{formatDate(membership.startDate)}</p>
                        </div>
                        <div>
                          <p className="opacity-75">
                            {membership.endDate ? 'Expires' : 'Status'}
                          </p>
                          <p className="font-semibold">
                            {membership.endDate 
                              ? `${getDaysRemaining(membership.endDate)} days left`
                              : 'Lifetime'
                            }
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Download Usage */}
                    <div className="bg-gray-800/50 p-6 rounded-xl">
                      <h4 className="text-white font-semibold mb-4 flex items-center">
                        <ArrowDownTrayIcon className="w-5 h-5 mr-2" />
                        Download Usage
                      </h4>
                      
                      <div className="space-y-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Daily Downloads</span>
                          <span className="text-white">
                            {membership.downloadCount} / {membership.downloadLimit}
                          </span>
                        </div>
                        
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${getDownloadPercentage(membership.downloadCount, membership.downloadLimit)}%` }}
                          />
                        </div>
                        
                        <p className="text-xs text-gray-400">
                          Resets daily at midnight UTC
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CrownIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">No Active Membership</h3>
                    <p className="text-gray-400 mb-6">
                      Upgrade to a membership plan to access premium features and increased download limits.
                    </p>
                    <Button 
                      onClick={() => router.push('/membership')}
                      className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                    >
                      View Membership Plans
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card className="bg-gray-900/50 border-gray-700/50">
              <CardHeader>
                <CardTitle className="text-white">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push('/membership')}
                >
                  <CrownIcon className="w-4 h-4 mr-2" />
                  View Plans
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push('/downloads')}
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                  My Downloads
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => router.push('/orders')}
                >
                  <ClockIcon className="w-4 h-4 mr-2" />
                  Order History
                </Button>
              </CardContent>
            </Card>

            {/* Membership Benefits */}
            {membership && (
              <Card className="bg-gray-900/50 border-gray-700/50">
                <CardHeader>
                  <CardTitle className="text-white">Your Benefits</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center text-green-400">
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      {membership.downloadLimit} downloads per day
                    </div>
                    <div className="flex items-center text-green-400">
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Premium support access
                    </div>
                    <div className="flex items-center text-green-400">
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Early access to new tools
                    </div>
                    {membership.type === 'GOLD' && (
                      <div className="flex items-center text-yellow-400">
                        <CheckCircleIcon className="w-4 h-4 mr-2" />
                        Exclusive VIP content
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Membership History */}
        {membershipHistory.length > 0 && (
          <div className="mt-8">
            <Card className="bg-gray-900/50 border-gray-700/50">
              <CardHeader>
                <CardTitle className="text-white">Membership History</CardTitle>
                <CardDescription>Your past membership subscriptions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {membershipHistory.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Badge className={membershipBadgeColors[item.type]}>
                          {item.type}
                        </Badge>
                        <div>
                          <p className="text-white font-medium">
                            {item.type} Membership
                          </p>
                          <p className="text-sm text-gray-400">
                            {formatDate(item.startDate)} - {item.endDate ? formatDate(item.endDate) : 'Ongoing'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant={item.status === 'ACTIVE' ? 'default' : 'secondary'}>
                          {item.status === 'ACTIVE' ? (
                            <CheckCircleIcon className="w-3 h-3 mr-1" />
                          ) : (
                            <XCircleIcon className="w-3 h-3 mr-1" />
                          )}
                          {item.status}
                        </Badge>
                        <p className="text-xs text-gray-400 mt-1">
                          via {item.paymentMethod}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
