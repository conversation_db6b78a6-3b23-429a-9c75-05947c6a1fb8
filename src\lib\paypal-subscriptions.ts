// PayPal Subscriptions API integration
import { getPayPalAccessToken } from '@/lib/paypal'

const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com'

// PayPal Plan IDs (these need to be created in PayPal dashboard)
const PAYPAL_PLAN_IDS = {
  BRONZE: process.env.PAYPAL_BRONZE_PLAN_ID || 'P-5ML4271244454362WXNWU5NQ',
  SILVER: process.env.PAYPAL_SILVER_PLAN_ID || 'P-1GJ4271244454362WXNWU5NR',
  GOLD: process.env.PAYPAL_GOLD_PLAN_ID || 'P-8HL4271244454362WXNWU5NS'
}

export interface PayPalSubscriptionData {
  id: string
  status: string
  status_update_time: string
  plan_id: string
  start_time: string
  quantity: string
  shipping_amount: {
    currency_code: string
    value: string
  }
  subscriber: {
    name: {
      given_name: string
      surname: string
    }
    email_address: string
    payer_id: string
  }
  billing_info: {
    outstanding_balance: {
      currency_code: string
      value: string
    }
    cycle_executions: Array<{
      tenure_type: string
      sequence: number
      cycles_completed: number
      cycles_remaining: number
      current_pricing_scheme_version: number
    }>
    last_payment: {
      amount: {
        currency_code: string
        value: string
      }
      time: string
    }
    next_billing_time: string
  }
  create_time: string
  update_time: string
  links: Array<{
    href: string
    rel: string
    method: string
  }>
}

/**
 * Create a PayPal subscription
 */
export async function createPayPalSubscription(
  planType: 'BRONZE' | 'SILVER' | 'GOLD',
  subscriber: {
    firstName: string
    lastName: string
    email: string
  },
  returnUrl?: string,
  cancelUrl?: string
): Promise<PayPalSubscriptionData> {
  try {
    console.log('[PayPal Subscriptions] Creating subscription:', { planType, email: subscriber.email })
    
    const accessToken = await getPayPalAccessToken()
    const planId = PAYPAL_PLAN_IDS[planType]

    if (!planId) {
      throw new Error(`PayPal plan ID not configured for ${planType}`)
    }

    const subscriptionData = {
      plan_id: planId,
      start_time: new Date(Date.now() + 60000).toISOString(), // Start 1 minute from now
      quantity: "1",
      subscriber: {
        name: {
          given_name: subscriber.firstName,
          surname: subscriber.lastName
        },
        email_address: subscriber.email
      },
      application_context: {
        brand_name: 'Forex Bot Zone',
        locale: 'en-US',
        shipping_preference: 'NO_SHIPPING',
        user_action: 'SUBSCRIBE_NOW',
        payment_method: {
          payer_selected: 'PAYPAL',
          payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
        },
        return_url: returnUrl || `${process.env.SITE_URL}/subscription/success`,
        cancel_url: cancelUrl || `${process.env.SITE_URL}/subscription`
      }
    }

    console.log('[PayPal Subscriptions] Subscription data:', JSON.stringify(subscriptionData, null, 2))

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'PayPal-Request-Id': `subscription-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      },
      body: JSON.stringify(subscriptionData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal Subscriptions] Subscription creation failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to create PayPal subscription: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('[PayPal Subscriptions] Subscription created successfully:', {
      id: result.id,
      status: result.status,
      planId: result.plan_id
    })

    return result
  } catch (error) {
    console.error('[PayPal Subscriptions] Error creating subscription:', error)
    throw error
  }
}

/**
 * Get PayPal subscription details
 */
export async function getPayPalSubscription(subscriptionId: string): Promise<PayPalSubscriptionData> {
  try {
    console.log('[PayPal Subscriptions] Getting subscription:', subscriptionId)
    
    const accessToken = await getPayPalAccessToken()

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal Subscriptions] Get subscription failed:', {
        subscriptionId,
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to get PayPal subscription: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('[PayPal Subscriptions] Subscription retrieved:', {
      id: result.id,
      status: result.status,
      nextBillingTime: result.billing_info?.next_billing_time
    })

    return result
  } catch (error) {
    console.error('[PayPal Subscriptions] Error getting subscription:', error)
    throw error
  }
}

/**
 * Cancel PayPal subscription
 */
export async function cancelPayPalSubscription(
  subscriptionId: string,
  reason: string = 'User requested cancellation'
): Promise<void> {
  try {
    console.log('[PayPal Subscriptions] Cancelling subscription:', subscriptionId)
    
    const accessToken = await getPayPalAccessToken()

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        reason: reason
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal Subscriptions] Cancel subscription failed:', {
        subscriptionId,
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to cancel PayPal subscription: ${response.status} ${errorText}`)
    }

    console.log('[PayPal Subscriptions] Subscription cancelled successfully:', subscriptionId)
  } catch (error) {
    console.error('[PayPal Subscriptions] Error cancelling subscription:', error)
    throw error
  }
}

/**
 * Activate PayPal subscription (after user approval)
 */
export async function activatePayPalSubscription(subscriptionId: string): Promise<PayPalSubscriptionData> {
  try {
    console.log('[PayPal Subscriptions] Activating subscription:', subscriptionId)
    
    const accessToken = await getPayPalAccessToken()

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}/activate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        reason: 'Subscription activated by user'
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal Subscriptions] Activate subscription failed:', {
        subscriptionId,
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to activate PayPal subscription: ${response.status} ${errorText}`)
    }

    // Get updated subscription details
    return await getPayPalSubscription(subscriptionId)
  } catch (error) {
    console.error('[PayPal Subscriptions] Error activating subscription:', error)
    throw error
  }
}

/**
 * Get subscription approval URL for user redirect
 */
export function getSubscriptionApprovalUrl(subscription: PayPalSubscriptionData): string | null {
  const approvalLink = subscription.links?.find(link => link.rel === 'approve')
  return approvalLink?.href || null
}

/**
 * Map PayPal subscription status to our internal status
 */
export function mapPayPalSubscriptionStatus(paypalStatus: string): 'PENDING' | 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'SUSPENDED' | 'PAST_DUE' {
  switch (paypalStatus.toLowerCase()) {
    case 'approval_pending':
      return 'PENDING'
    case 'approved':
    case 'active':
      return 'ACTIVE'
    case 'cancelled':
      return 'CANCELLED'
    case 'expired':
      return 'EXPIRED'
    case 'suspended':
      return 'SUSPENDED'
    default:
      return 'PENDING'
  }
}
