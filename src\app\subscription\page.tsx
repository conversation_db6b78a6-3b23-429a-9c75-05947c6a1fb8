'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface SubscriptionPlan {
  type: 'BRONZ<PERSON>' | 'SILVER' | 'GOLD'
  name: string
  price: number
  dailyDownloads: number
  features: string[]
  popular?: boolean
}

const plans: SubscriptionPlan[] = [
  {
    type: 'BRONZE',
    name: 'Bronze Plan',
    price: 29.99,
    dailyDownloads: 10,
    features: [
      'Access to all forex tools and EAs',
      '10 downloads per day',
      'Monthly billing',
      'Basic customer support',
      'VIP Telegram group access',
      'Regular updates and new tools'
    ]
  },
  {
    type: 'SILVER',
    name: 'Silver Plan',
    price: 39.99,
    dailyDownloads: 25,
    popular: true,
    features: [
      'Everything in Bronze Plan',
      '25 downloads per day',
      'Priority customer support',
      'Early access to new tools',
      'Advanced trading strategies',
      'Custom indicator requests'
    ]
  },
  {
    type: 'GOLD',
    name: 'Gold Plan',
    price: 49.99,
    dailyDownloads: 100,
    features: [
      'Everything in Silver Plan',
      '100 downloads per day',
      'Dedicated account manager',
      '1-on-1 trading consultation',
      'Custom EA development',
      'Backtesting services'
    ]
  }
]

export default function SubscriptionPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [subscribing, setSubscribing] = useState<string | null>(null)
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription')
      return
    }

    if (status === 'authenticated') {
      fetchSubscriptionStatus()
    }
  }, [status, router])

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/subscriptions/status')
      const data = await response.json()
      
      if (data.success) {
        setSubscriptionStatus(data)
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubscribe = async (planType: 'BRONZE' | 'SILVER' | 'GOLD') => {
    if (!session?.user) {
      router.push('/auth/signin?callbackUrl=/subscription')
      return
    }

    if (subscriptionStatus?.hasSubscription && subscriptionStatus.subscription?.isActive) {
      toast.error('You already have an active subscription')
      return
    }

    setSubscribing(planType)

    try {
      const response = await fetch('/api/subscriptions/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planType,
          customerInfo: {
            firstName: session.user.name?.split(' ')[0] || 'User',
            lastName: session.user.name?.split(' ').slice(1).join(' ') || '',
            email: session.user.email
          }
        }),
      })

      const data = await response.json()

      if (data.success && data.approvalUrl) {
        toast.success('Redirecting to PayPal for subscription setup...')
        // Redirect to PayPal for subscription approval
        window.location.href = data.approvalUrl
      } else {
        toast.error(data.message || 'Failed to create subscription')
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      toast.error('Failed to create subscription')
    } finally {
      setSubscribing(null)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-4">
            Choose Your <span className="text-yellow-400">Subscription Plan</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get unlimited access to our premium forex tools, EAs, and indicators with flexible monthly subscriptions.
          </p>
        </div>

        {/* Current Subscription Status */}
        {subscriptionStatus?.hasSubscription && (
          <div className="mb-12 bg-gradient-to-r from-green-900/20 to-blue-900/20 border border-green-500/30 rounded-2xl p-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-2">Current Subscription</h2>
              <p className="text-green-400 text-lg mb-4">
                {subscriptionStatus.subscription.plan.name} - ${subscriptionStatus.subscription.plan.price}/month
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Status:</span>
                  <span className={`ml-2 font-semibold ${
                    subscriptionStatus.subscription.isActive ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {subscriptionStatus.subscription.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Downloads Today:</span>
                  <span className="ml-2 font-semibold text-white">
                    {subscriptionStatus.downloadLimits.dailyLimit - subscriptionStatus.downloadLimits.remainingDownloads} / {subscriptionStatus.downloadLimits.dailyLimit}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Next Billing:</span>
                  <span className="ml-2 font-semibold text-white">
                    {subscriptionStatus.subscription.nextBillingDate 
                      ? new Date(subscriptionStatus.subscription.nextBillingDate).toLocaleDateString()
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>
              <div className="mt-4">
                <Button
                  onClick={() => router.push('/subscription/manage')}
                  variant="outline"
                  className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black"
                >
                  Manage Subscription
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Subscription Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div
              key={plan.type}
              className={`relative bg-gradient-to-b from-gray-800/50 to-gray-900/50 backdrop-blur-sm border rounded-2xl p-8 ${
                plan.popular
                  ? 'border-yellow-500/50 ring-2 ring-yellow-500/20'
                  : 'border-gray-700/50'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-yellow-400">${plan.price}</span>
                  <span className="text-gray-400 ml-2">/month</span>
                </div>
                <p className="text-gray-300">
                  {plan.dailyDownloads} downloads per day
                </p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckIcon className="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                onClick={() => handleSubscribe(plan.type)}
                disabled={subscribing === plan.type || (subscriptionStatus?.hasSubscription && subscriptionStatus.subscription?.isActive)}
                variant={plan.popular ? 'premium' : 'outline'}
                size="lg"
                className="w-full"
              >
                {subscribing === plan.type ? 'Processing...' :
                 (subscriptionStatus?.hasSubscription && subscriptionStatus.subscription?.planType === plan.type && subscriptionStatus.subscription?.isActive) ? 'Current Plan' :
                 'Subscribe Now'}
              </Button>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="text-center bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Why Choose Our Subscription?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckIcon className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Unlimited Access</h3>
              <p className="text-gray-300">
                Access to our entire library of forex tools, EAs, and indicators
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckIcon className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Regular Updates</h3>
              <p className="text-gray-300">
                Get the latest tools and updates as soon as they're released
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckIcon className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Cancel Anytime</h3>
              <p className="text-gray-300">
                No long-term commitments. Cancel your subscription anytime
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
