'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { 
  CreditCardIcon, 
  CalendarIcon, 
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

export default function ManageSubscriptionPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [subscriptionData, setSubscriptionData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [cancelling, setCancelling] = useState(false)
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription/manage')
      return
    }

    if (status === 'authenticated') {
      fetchSubscriptionData()
    }
  }, [status, router])

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscriptions/status')
      const data = await response.json()
      
      if (data.success) {
        setSubscriptionData(data)
        
        if (!data.hasSubscription) {
          router.push('/subscription')
          return
        }
      } else {
        toast.error('Failed to load subscription data')
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error)
      toast.error('Failed to load subscription data')
    } finally {
      setLoading(false)
    }
  }

  const handleCancelSubscription = async () => {
    setCancelling(true)
    
    try {
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'User requested cancellation from management page'
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        setShowCancelConfirm(false)
        // Refresh subscription data
        await fetchSubscriptionData()
      } else {
        toast.error(data.message || 'Failed to cancel subscription')
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error)
      toast.error('Failed to cancel subscription')
    } finally {
      setCancelling(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (!subscriptionData?.hasSubscription) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">No Active Subscription</h2>
          <p className="text-gray-400 mb-8">You don't have an active subscription.</p>
          <Button onClick={() => router.push('/subscription')} variant="premium">
            View Subscription Plans
          </Button>
        </div>
      </div>
    )
  }

  const subscription = subscriptionData.subscription
  const downloadLimits = subscriptionData.downloadLimits

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Manage Your <span className="text-yellow-400">Subscription</span>
          </h1>
          <p className="text-xl text-gray-300">
            View and manage your subscription details, usage, and billing information.
          </p>
        </div>

        {/* Subscription Status Card */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className={`w-4 h-4 rounded-full mr-3 ${
                subscription.isActive ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <h2 className="text-2xl font-bold text-white">
                {subscription.plan.name}
              </h2>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-yellow-400">
                ${subscription.plan.price}
              </div>
              <div className="text-gray-400">per month</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <CheckCircleIcon className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">Status</div>
              <div className={`text-sm ${subscription.isActive ? 'text-green-400' : 'text-red-400'}`}>
                {subscription.isActive ? 'Active' : 'Inactive'}
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <CalendarIcon className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">Next Billing</div>
              <div className="text-sm text-gray-300">
                {subscription.nextBillingDate 
                  ? new Date(subscription.nextBillingDate).toLocaleDateString()
                  : 'N/A'
                }
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <CreditCardIcon className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">Payment Method</div>
              <div className="text-sm text-gray-300">PayPal</div>
            </div>
          </div>

          {subscription.cancelAtPeriodEnd && (
            <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="w-6 h-6 text-yellow-400 mr-3" />
                <div>
                  <div className="text-yellow-400 font-semibold">Subscription Cancelled</div>
                  <div className="text-gray-300 text-sm">
                    Your subscription will end on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}.
                    You'll retain access until then.
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Usage Statistics */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
            <ChartBarIcon className="w-6 h-6 mr-3" />
            Usage Statistics
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-300">Daily Downloads</span>
                <span className="text-white font-semibold">
                  {downloadLimits.dailyLimit - downloadLimits.remainingDownloads} / {downloadLimits.dailyLimit}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${((downloadLimits.dailyLimit - downloadLimits.remainingDownloads) / downloadLimits.dailyLimit) * 100}%` 
                  }}
                ></div>
              </div>
              <div className="text-sm text-gray-400 mt-1">
                Resets at {downloadLimits.resetTime ? new Date(downloadLimits.resetTime).toLocaleTimeString() : 'midnight'}
              </div>
            </div>
            
            <div>
              <div className="text-center p-4 bg-gray-800/30 rounded-lg">
                <div className="text-2xl font-bold text-yellow-400">
                  {downloadLimits.remainingDownloads}
                </div>
                <div className="text-gray-300">Downloads Remaining Today</div>
              </div>
            </div>
          </div>
        </div>

        {/* Plan Features */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-white mb-6">Your Plan Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {subscription.plan.features.map((feature: string, index: number) => (
              <div key={index} className="flex items-center">
                <CheckCircleIcon className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                <span className="text-gray-300">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => router.push('/subscription')}
            variant="outline"
            className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black"
          >
            View All Plans
          </Button>
          
          {subscription.isActive && !subscription.cancelAtPeriodEnd && (
            <Button
              onClick={() => setShowCancelConfirm(true)}
              variant="outline"
              className="border-red-400 text-red-400 hover:bg-red-400 hover:text-white"
            >
              Cancel Subscription
            </Button>
          )}
        </div>

        {/* Cancel Confirmation Modal */}
        {showCancelConfirm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-2xl p-8 max-w-md w-full">
              <div className="text-center mb-6">
                <XCircleIcon className="w-16 h-16 text-red-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Cancel Subscription?</h3>
                <p className="text-gray-300">
                  Are you sure you want to cancel your subscription? You'll retain access until the end of your current billing period.
                </p>
              </div>
              
              <div className="flex gap-4">
                <Button
                  onClick={() => setShowCancelConfirm(false)}
                  variant="outline"
                  className="flex-1"
                  disabled={cancelling}
                >
                  Keep Subscription
                </Button>
                <Button
                  onClick={handleCancelSubscription}
                  variant="outline"
                  className="flex-1 border-red-400 text-red-400 hover:bg-red-400 hover:text-white"
                  disabled={cancelling}
                >
                  {cancelling ? 'Cancelling...' : 'Yes, Cancel'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
