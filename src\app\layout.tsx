import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { SessionProvider } from '@/components/providers/session-provider'
import { WishlistProvider } from '@/components/providers/wishlist-provider'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Toaster } from 'react-hot-toast'
import { ConditionalLayout } from '@/components/layout/conditional-layout'
import { ErrorBoundary } from '@/components/error-boundary'
import { HydrationProvider } from '@/components/providers/hydration-provider'
import { OrganizationStructuredData, WebsiteStructuredData } from '@/components/seo/structured-data'


const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Forex Bot Zone - Premium EA & Indicators at Special Prices',
    template: '%s | Forex Bot Zone'
  },
  description: 'Get Premium Forex Expert Advisors and Indicators at unbeatable special prices. Professional MT4/MT5 trading bots and tools for serious traders.',
  keywords: [
    'forex bot',
    'expert advisor',
    'EA',
    'indicators',
    'MT4',
    'MT5',
    'trading robot',
    'automated trading',
    'special price',
    'premium forex tools',
    'forex signals',
    'trading strategy',
    'metatrader',
    'algorithmic trading'
  ].join(', '),
  authors: [{ name: 'Forex Bot Zone', url: process.env.SITE_URL || 'http://localhost:3000' }],
  creator: 'Forex Bot Zone',
  publisher: 'Forex Bot Zone',
  applicationName: 'Forex Bot Zone',
  referrer: 'origin-when-cross-origin',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.SITE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  verification: {
    // Add your verification codes here when available
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
    // yahoo: 'your-yahoo-verification-code',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Forex Bot Zone - Premium EA & Indicators at Special Prices',
    description: 'Get Premium Forex Expert Advisors and Indicators at unbeatable special prices.',
    siteName: 'Forex Bot Zone',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Forex Bot Zone',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Forex Bot Zone - Premium EA & Indicators at Special Prices',
    description: 'Get Premium Forex Expert Advisors and Indicators at unbeatable special prices.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#000000" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <OrganizationStructuredData baseUrl={baseUrl} />
        <WebsiteStructuredData baseUrl={baseUrl} />
      </head>
      <body className={`${inter.className} antialiased`}>
        <SessionProvider>
          <WishlistProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem
              disableTransitionOnChange
            >
              <HydrationProvider>
                <ErrorBoundary>
                  <ConditionalLayout>
                    {children}
                  </ConditionalLayout>
                </ErrorBoundary>
              </HydrationProvider>
            </ThemeProvider>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#1f2937',
                  color: '#f9fafb',
                  border: '1px solid #374151',
                },
                success: {
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#f9fafb',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#f9fafb',
                  },
                },
              }}
            />
          </WishlistProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
