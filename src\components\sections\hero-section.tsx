'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import {
  ChartBarIcon,
  TrophyIcon,
  ArrowRightIcon,
  StarIcon,
  ShieldCheckIcon,
  BoltIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'

// Hero slideshow images
const heroImages = [
  {
    id: 1,
    src: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=600&fit=crop&crop=center',
    alt: 'Forex Trading Dashboard',
    title: 'Advanced Trading Tools',
    subtitle: 'Professional MT4/MT5 Expert Advisors'
  },
  {
    id: 2,
    src: 'https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=600&fit=crop&crop=center',
    alt: 'Market Analysis',
    title: 'Smart Market Analysis',
    subtitle: 'AI-Powered Trading Indicators'
  },
  {
    id: 3,
    src: 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=800&h=600&fit=crop&crop=center',
    alt: 'Trading Success',
    title: 'Proven Results',
    subtitle: 'Join 25,000+ Successful Traders'
  }
]



const features = [
  {
    icon: BoltIcon,
    title: 'Instant Download',
    description: 'Get your tools immediately after purchase'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Tested & Verified',
    description: 'All EAs thoroughly tested for performance'
  },
  {
    icon: StarIcon,
    title: '24/7 Support',
    description: 'Expert assistance whenever you need it'
  },
]

export function HeroSection() {
  const [isVisible, setIsVisible] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  // Auto-advance slideshow
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroImages.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroImages.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroImages.length) % heroImages.length)
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 min-h-screen flex items-center">
        <div className={`w-full transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>

          {/* Two Column Layout */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">

            {/* Left Column - Content */}
            <div className="text-center lg:text-left space-y-8">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-yellow-500/10 border border-yellow-500/20 rounded-full text-yellow-400 text-sm font-medium">
                <TrophyIcon className="w-4 h-4 mr-2" />
                #1 Premium Forex Bot Store
              </div>

              {/* Main Heading */}
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight">
                  Premium{' '}
                  <span className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Forex Bots
                  </span>
                  <br />
                  <span className="text-gray-300 text-3xl sm:text-4xl lg:text-5xl">
                    Special Prices
                  </span>
                </h1>

                <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                  Get premium Expert Advisors and Indicators for MT4/MT5 at unbeatable prices.
                  Join 25,000+ successful traders worldwide.
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/products">
                  <Button variant="premium" size="lg" className="group w-full sm:w-auto text-lg px-8 py-4">
                    Shop Premium Bots
                    <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                  </Button>
                </Link>

                <Link href="/membership">
                  <Button variant="glass" size="lg" className="group w-full sm:w-auto text-lg px-8 py-4">
                    <StarIcon className="w-5 h-5 mr-2" />
                    VIP Membership
                  </Button>
                </Link>
              </div>

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-8">
                {features.map((feature, index) => (
                  <div
                    key={feature.title}
                    className={`text-center lg:text-left transition-all duration-500 ${
                      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                    }`}
                    style={{ transitionDelay: `${index * 200 + 600}ms` }}
                  >
                    <div className="flex items-center justify-center lg:justify-start mb-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                        <feature.icon className="w-4 h-4 text-black" />
                      </div>
                    </div>
                    <h3 className="text-white font-semibold text-sm mb-1">{feature.title}</h3>
                    <p className="text-gray-400 text-xs">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column - Image Slideshow */}
            <div className="relative">
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gray-800/50 backdrop-blur-sm border border-gray-700/50">
                {/* Main Image */}
                <div className="relative w-full h-full">
                  {heroImages.map((image, index) => (
                    <div
                      key={image.id}
                      className={`absolute inset-0 transition-all duration-1000 ${
                        index === currentSlide
                          ? 'opacity-100 scale-100'
                          : 'opacity-0 scale-105'
                      }`}
                    >
                      <Image
                        src={image.src}
                        alt={image.alt}
                        fill
                        className="object-cover"
                        priority={index === 0}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                      {/* Image Content */}
                      <div className="absolute bottom-6 left-6 right-6 text-white">
                        <h3 className="text-xl font-bold mb-2">{image.title}</h3>
                        <p className="text-gray-200 text-sm">{image.subtitle}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>

                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
                >
                  <ChevronRightIcon className="w-5 h-5" />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
                  {heroImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToSlide(index)}
                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                        index === currentSlide
                          ? 'bg-yellow-400 w-6'
                          : 'bg-white/50 hover:bg-white/70'
                      }`}
                    />
                  ))}
                </div>

                {/* Glow Effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-2xl blur-xl opacity-50" />
              </div>

              {/* Floating Stats */}
              <div className="absolute -bottom-8 -left-8 bg-gray-900/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 hidden lg:block">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
                    <TrophyIcon className="w-5 h-5 text-black" />
                  </div>
                  <div>
                    <p className="text-white font-semibold text-sm">98% Success Rate</p>
                    <p className="text-gray-400 text-xs">Verified Results</p>
                  </div>
                </div>
              </div>

              <div className="absolute -top-8 -right-8 bg-gray-900/90 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 hidden lg:block">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center">
                    <ChartBarIcon className="w-5 h-5 text-black" />
                  </div>
                  <div>
                    <p className="text-white font-semibold text-sm">25,000+ Traders</p>
                    <p className="text-gray-400 text-xs">Active Community</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce hidden lg:block">
        <div className="w-6 h-10 border-2 border-white/20 rounded-full flex justify-center backdrop-blur-sm">
          <div className="w-1 h-3 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}
