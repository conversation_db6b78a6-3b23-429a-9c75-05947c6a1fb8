/**
 * @deprecated This admin membership API is deprecated in favor of the new subscription system.
 * This endpoint is kept for backward compatibility and will be removed in a future version.
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/memberships/stats - Get membership statistics (DEPRECATED)
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get membership statistics from database
    const totalMembers = await prisma.membership.count()
    const activeMembers = await prisma.membership.count({
      where: {
        status: 'ACTIVE',
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      }
    })

    // Get members by type
    const membersByType = await prisma.membership.groupBy({
      by: ['type'],
      where: {
        status: 'ACTIVE',
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      },
      _count: {
        id: true
      }
    })

    const byType = {
      BRONZE: 0,
      SILVER: 0,
      GOLD: 0
    }

    membersByType.forEach(group => {
      byType[group.type] = group._count.id
    })

    // Calculate estimated monthly revenue (simplified calculation)
    // In a real implementation, you'd calculate based on actual subscription data
    const revenue = (byType.BRONZE * 299.99) + (byType.SILVER * 399.99) + (byType.GOLD * 499.99)

    const stats = {
      totalMembers,
      activeMembers,
      revenue: Math.round(revenue),
      byType
    }

    return NextResponse.json({ success: true, stats })
  } catch (error) {
    console.error('Error fetching membership stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch membership statistics' },
      { status: 500 }
    )
  }
}
