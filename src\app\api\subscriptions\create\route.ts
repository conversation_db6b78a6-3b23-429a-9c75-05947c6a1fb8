import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPayPalSubscription, getSubscriptionApprovalUrl } from '@/lib/paypal-subscriptions'
import { createSubscription, getUserSubscription, getSubscriptionPlan } from '@/lib/subscription-manager'
import { z } from 'zod'
import { securityHeaders, rateLimit, createSafeErrorResponse } from '@/lib/security'

const createSubscriptionSchema = z.object({
  planType: z.enum(['BRONZE', 'SILVER', 'GOLD']),
  customerInfo: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    email: z.string().email()
  })
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5 // 5 subscription attempts per 15 minutes per IP
    })(request)

    if (!rateLimitResult.success) {
      return NextResponse.json({
        success: false,
        message: rateLimitResult.error
      }, { 
        status: 429,
        headers: securityHeaders
      })
    }

    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { 
        status: 401,
        headers: securityHeaders
      })
    }

    const body = await request.json()
    const { planType, customerInfo } = createSubscriptionSchema.parse(body)

    console.log('[Subscription API] Creating subscription:', { 
      userId: session.user.id, 
      planType, 
      email: customerInfo.email 
    })

    // Check if user already has an active subscription
    const existingSubscription = await getUserSubscription(session.user.id)
    if (existingSubscription && existingSubscription.isActive) {
      return NextResponse.json({
        success: false,
        message: 'You already have an active subscription'
      }, { 
        status: 400,
        headers: securityHeaders
      })
    }

    // Get plan details
    const plan = getSubscriptionPlan(planType)
    
    try {
      // Create PayPal subscription
      const paypalSubscription = await createPayPalSubscription(
        planType,
        {
          firstName: customerInfo.firstName,
          lastName: customerInfo.lastName,
          email: customerInfo.email
        },
        `${process.env.SITE_URL}/api/subscriptions/paypal/success`,
        `${process.env.SITE_URL}/membership/cancel`
      )

      console.log('[Subscription API] PayPal subscription created:', {
        id: paypalSubscription.id,
        status: paypalSubscription.status
      })

      // Calculate subscription period (monthly billing)
      const currentPeriodStart = new Date()
      const currentPeriodEnd = new Date()
      currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)

      // Create subscription record in our database
      const subscription = await createSubscription(
        session.user.id,
        planType,
        paypalSubscription.id,
        currentPeriodStart,
        currentPeriodEnd
      )

      console.log('[Subscription API] Subscription record created:', {
        id: subscription.id,
        paypalSubscriptionId: paypalSubscription.id
      })

      // Get approval URL for user redirect
      const approvalUrl = getSubscriptionApprovalUrl(paypalSubscription)
      
      if (!approvalUrl) {
        throw new Error('No approval URL found in PayPal response')
      }

      return NextResponse.json({
        success: true,
        subscription: {
          id: subscription.id,
          planType: subscription.planType,
          status: subscription.status,
          paypalSubscriptionId: paypalSubscription.id
        },
        plan: {
          name: plan.name,
          price: plan.price,
          dailyDownloads: plan.dailyDownloads,
          features: plan.features
        },
        approvalUrl,
        message: 'Subscription created successfully. Please complete payment with PayPal.'
      }, {
        headers: securityHeaders
      })

    } catch (paypalError: any) {
      console.error('[Subscription API] PayPal subscription creation failed:', paypalError)
      
      return NextResponse.json({
        success: false,
        message: 'Failed to create subscription with PayPal. Please try again.',
        error: process.env.NODE_ENV === 'development' ? paypalError.message : undefined
      }, { 
        status: 503,
        headers: securityHeaders
      })
    }

  } catch (error: any) {
    console.error('[Subscription API] Error creating subscription:', {
      error: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString()
    })

    if (error.name === 'ZodError') {
      console.error('[Subscription API] Validation error:', error.errors)
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
