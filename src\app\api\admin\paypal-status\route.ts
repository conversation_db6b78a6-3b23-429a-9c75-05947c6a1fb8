import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { securityHeaders } from '@/lib/security'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, {
        status: 401,
        headers: securityHeaders
      })
    }

    const paypalClientId = process.env.PAYPAL_CLIENT_ID
    const paypalClientSecret = process.env.PAYPAL_CLIENT_SECRET
    const environment = process.env.NODE_ENV === 'production' ? 'Production' : 'Sandbox'

    return NextResponse.json({
      success: true,
      data: {
        configured: !!(paypalClientId && paypalClientSecret),
        environment,
        clientIdConfigured: !!paypalClientId,
        clientSecretConfigured: !!paypalClientSecret
      }
    }, {
      headers: securityHeaders
    })

  } catch (error) {
    console.error('Error getting PayPal status:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Failed to get PayPal status'
    }, {
      status: 500,
      headers: securityHeaders
    })
  }
}
