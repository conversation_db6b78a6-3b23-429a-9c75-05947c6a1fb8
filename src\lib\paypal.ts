// PayPal configuration and utilities
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com'

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.warn('PayPal credentials not configured')
}

export const getPayPalClientId = () => {
  if (!PAYPAL_CLIENT_ID) {
    throw new Error('PAYPAL_CLIENT_ID is not set in environment variables')
  }
  return PAYPAL_CLIENT_ID
}

// Get PayPal access token
export async function getPayPalAccessToken(): Promise<string> {
  if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
    console.error('[PayPal] Credentials not configured')
    throw new Error('PayPal credentials not configured')
  }

  try {
    console.log('[PayPal] Requesting access token from:', PAYPAL_BASE_URL)
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64')

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal] Access token request failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to get PayPal access token: ${response.status} ${errorText}`)
    }

    const data = await response.json()
    console.log('[PayPal] Access token obtained successfully')
    return data.access_token
  } catch (error) {
    console.error('[PayPal] Error getting access token:', error)
    throw error
  }
}

// Create PayPal order
export async function createPayPalOrder(
  amount: number,
  currency: string = 'USD',
  items: Array<{
    name: string
    quantity: number
    unit_amount: {
      currency_code: string
      value: string
    }
  }> = [],
  orderId?: string
) {
  try {
    console.log('[PayPal] Creating order:', { amount, currency, itemCount: items.length, orderId })
    const accessToken = await getPayPalAccessToken()

    // Calculate item total for PayPal validation
    const itemTotal = items.reduce((total, item) => {
      return total + (parseFloat(item.unit_amount.value) * item.quantity)
    }, 0)

    const orderData = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          reference_id: orderId || 'default',
          amount: {
            currency_code: currency,
            value: amount.toFixed(2),
            breakdown: items.length > 0 ? {
              item_total: {
                currency_code: currency,
                value: itemTotal.toFixed(2)
              },
              discount: itemTotal > amount ? {
                currency_code: currency,
                value: (itemTotal - amount).toFixed(2)
              } : undefined
            } : undefined
          },
          items: items.length > 0 ? items : undefined
        }
      ],
      application_context: {
        brand_name: 'Forex Bot Zone',
        landing_page: 'NO_PREFERENCE',
        user_action: 'PAY_NOW',
        return_url: `${process.env.SITE_URL}/api/paypal/success`,
        cancel_url: `${process.env.SITE_URL}/api/paypal/cancel`
      }
    }

    console.log('[PayPal] Order data prepared:', JSON.stringify(orderData, null, 2))

    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal] Order creation failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      throw new Error(`Failed to create PayPal order: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('[PayPal] Order created successfully:', { id: result.id, status: result.status })
    return result
  } catch (error) {
    console.error('[PayPal] Error creating order:', error)
    throw error
  }
}

// Capture PayPal order
export async function capturePayPalOrder(paypalOrderId: string) {
  try {
    console.log('[PayPal] Capturing order:', paypalOrderId)
    const accessToken = await getPayPalAccessToken()

    // First, get order details to verify it's ready for capture
    const orderDetailsResponse = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${paypalOrderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (!orderDetailsResponse.ok) {
      const errorText = await orderDetailsResponse.text()
      console.error('[PayPal] Failed to get order details:', {
        orderId: paypalOrderId,
        status: orderDetailsResponse.status,
        error: errorText
      })
      throw new Error(`Failed to get PayPal order details: ${orderDetailsResponse.status} ${errorText}`)
    }

    const orderDetails = await orderDetailsResponse.json()
    console.log('[PayPal] Order details:', {
      id: orderDetails.id,
      status: orderDetails.status,
      intent: orderDetails.intent
    })

    // Check if order is in the correct state for capture
    if (orderDetails.status !== 'APPROVED') {
      console.error('[PayPal] Order not approved for capture:', {
        orderId: paypalOrderId,
        status: orderDetails.status
      })
      throw new Error(`Order is not approved for capture. Status: ${orderDetails.status}`)
    }

    // Now capture the payment
    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${paypalOrderId}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `${paypalOrderId}-${Date.now()}`, // Idempotency key
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('[PayPal] Order capture failed:', {
        orderId: paypalOrderId,
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })

      // Parse error details if available
      let errorDetails = errorText
      try {
        const errorJson = JSON.parse(errorText)
        errorDetails = errorJson.details?.[0]?.description || errorJson.message || errorText
      } catch (e) {
        // Keep original error text
      }

      throw new Error(`Failed to capture PayPal order: ${response.status} - ${errorDetails}`)
    }

    const result = await response.json()
    console.log('[PayPal] Order captured successfully:', {
      id: result.id,
      status: result.status,
      captureId: result.purchase_units?.[0]?.payments?.captures?.[0]?.id,
      captureStatus: result.purchase_units?.[0]?.payments?.captures?.[0]?.status
    })

    // Verify capture was successful
    const capture = result.purchase_units?.[0]?.payments?.captures?.[0]
    if (!capture) {
      throw new Error('No capture found in PayPal response')
    }

    if (capture.status === 'PENDING') {
      console.warn('[PayPal] Capture is pending:', {
        captureId: capture.id,
        reason: capture.status_details?.reason
      })
      // For pending captures, we'll handle this in webhook
    } else if (capture.status !== 'COMPLETED') {
      throw new Error(`Capture failed with status: ${capture.status}`)
    }

    return result
  } catch (error) {
    console.error('[PayPal] Error capturing order:', error)
    throw error
  }
}

// Get PayPal order details
export async function getPayPalOrder(paypalOrderId: string) {
  const accessToken = await getPayPalAccessToken()

  const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${paypalOrderId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to get PayPal order: ${error}`)
  }

  return await response.json()
}

// Verify PayPal webhook signature
export async function verifyPayPalWebhook(
  headers: Record<string, string>,
  body: string,
  webhookId: string
): Promise<boolean> {
  try {
    const accessToken = await getPayPalAccessToken()

    const verificationData = {
      auth_algo: headers['paypal-auth-algo'],
      cert_id: headers['paypal-cert-id'],
      transmission_id: headers['paypal-transmission-id'],
      transmission_sig: headers['paypal-transmission-sig'],
      transmission_time: headers['paypal-transmission-time'],
      webhook_id: webhookId,
      webhook_event: JSON.parse(body)
    }

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/notifications/verify-webhook-signature`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(verificationData),
    })

    if (!response.ok) {
      return false
    }

    const result = await response.json()
    return result.verification_status === 'SUCCESS'
  } catch (error) {
    console.error('PayPal webhook verification error:', error)
    return false
  }
}

// PayPal order status types
export type PayPalOrderStatus = 
  | 'CREATED'
  | 'SAVED'
  | 'APPROVED'
  | 'VOIDED'
  | 'COMPLETED'
  | 'PAYER_ACTION_REQUIRED'

export interface PayPalOrderResponse {
  id: string
  status: PayPalOrderStatus
  links: Array<{
    href: string
    rel: string
    method: string
  }>
  purchase_units: Array<{
    reference_id: string
    amount: {
      currency_code: string
      value: string
    }
    payments?: {
      captures: Array<{
        id: string
        status: string
        amount: {
          currency_code: string
          value: string
        }
      }>
    }
  }>
}
