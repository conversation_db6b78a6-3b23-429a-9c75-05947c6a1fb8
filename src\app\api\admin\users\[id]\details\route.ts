import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const userId = params.id

    // Fetch user orders
    const orders = await prisma.order.findMany({
      where: { userId },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10 // Limit to recent 10 orders
    })

    // Calculate stats
    const allOrders = await prisma.order.findMany({
      where: { userId },
      select: {
        totalAmount: true,
        status: true
      }
    })

    const stats = {
      totalOrders: allOrders.length,
      totalSpent: allOrders.reduce((sum, order) => sum + Number(order.totalAmount), 0),
      completedOrders: allOrders.filter(order => order.status === 'COMPLETED').length
    }

    // Serialize orders for JSON response
    const serializedOrders = orders.map(order => ({
      ...order,
      totalAmount: Number(order.totalAmount)
    }))

    return NextResponse.json({
      success: true,
      data: {
        orders: serializedOrders,
        stats
      }
    })

  } catch (error: any) {
    console.error('Error fetching user details:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch user details'
    }, { status: 500 })
  }
}
