import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updatePlanSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  type: z.enum(['BRONZE', 'SILVER', 'GOLD']).optional(),
  price: z.number().min(0, 'Price must be positive').optional(),
  duration: z.number().min(1, 'Duration must be at least 1 month').optional(),
  maxDailyDownloads: z.number().min(1, 'Max daily downloads must be at least 1').optional(),
  features: z.array(z.string()).min(1, 'At least one feature is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  isActive: z.boolean().optional()
})

// PUT /api/admin/memberships/plans/[id] - Update membership plan
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updatePlanSchema.parse(body)

    // For now, return success response
    // In a real implementation, you would update in database
    const updatedPlan = {
      id: params.id,
      ...validatedData,
      updatedAt: new Date()
    }

    return NextResponse.json({ 
      success: true, 
      plan: updatedPlan,
      message: 'Membership plan updated successfully' 
    })
  } catch (error) {
    console.error('Error updating membership plan:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update membership plan' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/memberships/plans/[id] - Delete membership plan
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // For now, return success response
    // In a real implementation, you would:
    // 1. Check if plan has active members
    // 2. Either prevent deletion or handle gracefully
    // 3. Delete from database

    return NextResponse.json({ 
      success: true,
      message: 'Membership plan deleted successfully' 
    })
  } catch (error) {
    console.error('Error deleting membership plan:', error)
    return NextResponse.json(
      { error: 'Failed to delete membership plan' },
      { status: 500 }
    )
  }
}
