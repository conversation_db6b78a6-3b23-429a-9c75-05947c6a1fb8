'use client'

import { useState } from 'react'
import { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js'
import { LockClosedIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface PayPalPaymentFormProps {
  amount: number
  items: Array<{
    productId: string
    quantity: number
  }>
  customerInfo: {
    email: string
    firstName: string
    lastName: string
  }
  couponCode?: string
  onSuccess: () => void
  onError: (error: string) => void
}

export function PayPalPaymentForm({
  amount,
  items,
  customerInfo,
  couponCode,
  onSuccess,
  onError
}: PayPalPaymentFormProps) {
  const [isProcessing, setIsProcessing] = useState(false)

  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID

  if (!clientId) {
    return (
      <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-6 text-center">
        <p className="text-red-400">PayPal configuration error. Please contact support.</p>
      </div>
    )
  }

  const createOrder = async () => {
    try {
      setIsProcessing(true)
      console.log('[PayPal Form] Creating order:', { amount, itemCount: items.length })

      const response = await fetch('/api/paypal/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          items,
          customerInfo,
          couponCode
        }),
      })

      const data = await response.json()
      console.log('[PayPal Form] Create order response:', data)

      if (!data.success) {
        console.error('[PayPal Form] Order creation failed:', data)

        // Provide more specific error messages
        let errorMessage = data.message || 'Failed to create order'
        if (data.message?.includes('Authentication required')) {
          errorMessage = 'Please sign in to continue with payment.'
        } else if (data.message?.includes('not available')) {
          errorMessage = 'Some products are no longer available.'
        } else if (data.message?.includes('Invalid order amount')) {
          errorMessage = 'Invalid order total. Please refresh and try again.'
        }

        throw new Error(errorMessage)
      }

      console.log('[PayPal Form] Order created successfully:', data.orderId)
      return data.orderId
    } catch (error) {
      console.error('[PayPal Form] Create order error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create order'

      toast.error(errorMessage)
      onError(errorMessage)
      throw error
    } finally {
      setIsProcessing(false)
    }
  }

  const onApprove = async (data: any) => {
    try {
      setIsProcessing(true)
      console.log('[PayPal Form] Payment approved, capturing order:', data.orderID)

      const response = await fetch('/api/paypal/capture-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderID: data.orderID,
          items,
          customerInfo,
          couponCode
        }),
      })

      const result = await response.json()
      console.log('[PayPal Form] Capture response:', result)

      if (result.success) {
        if (result.pending) {
          toast.success(result.message || 'Payment is being processed. You will receive confirmation once completed.')
          // Still call onSuccess to redirect user, but with pending status
          onSuccess(result.order)
        } else {
          toast.success('Payment successful!')
          onSuccess(result.order)
        }
      } else {
        console.error('[PayPal Form] Capture failed:', result)

        // Provide more specific error messages
        let errorMessage = result.message || 'Payment capture failed'
        if (result.message?.includes('Order not found')) {
          errorMessage = 'Payment session expired. Please try again.'
        } else if (result.message?.includes('already processed')) {
          errorMessage = 'This payment has already been processed.'
        } else if (result.message?.includes('not approved')) {
          errorMessage = 'Payment was not approved. Please try again.'
        }

        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('[PayPal Form] Capture order error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Payment capture failed'

      // Show user-friendly error message
      toast.error(errorMessage)
      onError(errorMessage)
    } finally {
      setIsProcessing(false)
    }
  }

  const onPayPalError = (err: any) => {
    console.error('PayPal error:', err)
    setIsProcessing(false)
    onError('PayPal payment failed')
  }

  const onCancel = () => {
    setIsProcessing(false)
    toast.error('Payment cancelled')
  }

  return (
    <PayPalScriptProvider
      options={{
        clientId,
        currency: 'USD',
        intent: 'capture',
        components: 'buttons',
        'disable-funding': 'credit,card'
      }}
    >
      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-500/30 rounded-xl p-6">
          <div className="flex items-center justify-center mb-6">
            <LockClosedIcon className="h-5 w-5 text-green-400 mr-2" />
            <span className="text-sm text-gray-300 font-medium">Secure payment with PayPal</span>
          </div>

          <div className="paypal-button-container">
            <PayPalButtons
              style={{
                layout: 'vertical',
                color: 'gold',
                shape: 'rect',
                label: 'paypal',
                height: 50,
                tagline: false
              }}
              createOrder={createOrder}
              onApprove={onApprove}
              onError={onPayPalError}
              onCancel={onCancel}
              disabled={isProcessing}
            />
          </div>

          {isProcessing && (
            <div className="mt-6 flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-400"></div>
              <span className="ml-2 text-sm text-gray-300">Processing payment...</span>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-400">
            By clicking the PayPal button, you agree to our{' '}
            <span className="text-yellow-400 hover:text-yellow-300 cursor-pointer">terms of service</span>
            {' '}and{' '}
            <span className="text-yellow-400 hover:text-yellow-300 cursor-pointer">privacy policy</span>.
          </p>
        </div>
      </div>
    </PayPalScriptProvider>
  )
}
