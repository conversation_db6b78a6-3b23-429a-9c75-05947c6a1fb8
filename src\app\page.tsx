import { Metadata } from 'next'
import { HeroSection } from '@/components/sections/hero-section'
import { TestimonialSlider } from '@/components/sections/testimonial-slider'
import { ProductCard } from '@/components/products/product-card'
import { getFeaturedProducts } from '@/lib/product-utils'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { BreadcrumbStructuredData } from '@/components/seo/structured-data'

export const metadata: Metadata = {
  title: 'Premium Forex Expert Advisors & Indicators at Special Prices',
  description: 'Get premium Forex Expert Advisors, indicators, and trading robots for MT4/MT5 at unbeatable special prices. Professional automated trading tools for serious traders with proven results.',
  keywords: 'forex bot, expert advisor, EA, indicators, MT4, MT5, trading robot, automated trading, special price, premium forex tools, forex signals, trading strategy, metatrader, algorithmic trading',
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Forex Bot Zone - Premium EA & Indicators at Special Prices',
    description: 'Get premium Forex Expert Advisors and indicators at unbeatable special prices. Professional trading tools for serious traders.',
    url: '/',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Forex Bot Zone - Premium EA & Indicators at Special Prices',
    description: 'Get premium Forex Expert Advisors and indicators at unbeatable special prices.',
  },
}

export default async function HomePage() {
  const featuredProducts = await getFeaturedProducts(8)
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  // Breadcrumb data for home page
  const breadcrumbItems = [
    { name: 'Home', url: '/' }
  ]

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} baseUrl={baseUrl} />

      <div>
      {/* Hero Section */}
      <HeroSection />



      {/* Live Forex Rates */}
      <section className="py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold text-white">Live Forex Rates</h3>
                  <p className="text-gray-400 text-sm">Major currency pairs</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Live</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-6">
              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">EUR/USD</h4>
                  <div className="flex items-center text-green-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">1.0852</div>
                  <div className="text-xs text-green-400">+0.12%</div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">GBP/USD</h4>
                  <div className="flex items-center text-red-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">1.2648</div>
                  <div className="text-xs text-red-400">-0.08%</div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">USD/JPY</h4>
                  <div className="flex items-center text-green-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">149.52</div>
                  <div className="text-xs text-green-400">+0.25%</div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">USD/CHF</h4>
                  <div className="flex items-center text-red-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">0.8948</div>
                  <div className="text-xs text-red-400">-0.15%</div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">AUD/USD</h4>
                  <div className="flex items-center text-green-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">0.6553</div>
                  <div className="text-xs text-green-400">+0.18%</div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white text-sm">USD/CAD</h4>
                  <div className="flex items-center text-red-400">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-white">1.3652</div>
                  <div className="text-xs text-red-400">-0.09%</div>
                </div>
              </div>
            </div>

            <div className="px-6 py-3 bg-gray-800/30 border-t border-gray-700/50">
              <p className="text-xs text-gray-400 text-center">
                * Prices are indicative and for educational purposes only.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              Featured Premium Bots
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Discover our most popular Expert Advisors and Indicators at special prices - trusted by professional traders worldwide
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          <div className="text-center">
            <Link href="/products">
              <Button variant="premium" size="lg">
                Shop All Premium Bots
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            Why Choose Our Platform?
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="trading-card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Premium Quality</h3>
              <p className="text-gray-400">
                Hand-picked Expert Advisors and Indicators tested by professional traders
              </p>
            </div>

            <div className="trading-card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Secure Downloads</h3>
              <p className="text-gray-400">
                Protected digital downloads with instant access after purchase
              </p>
            </div>

            <div className="trading-card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Community Support</h3>
              <p className="text-gray-400">
                Join our exclusive community of successful traders and get support
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialSlider />

      {/* CTA Section */}
      <section className="py-16 px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Trading?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of successful traders who trust our premium tools
          </p>
          <Link href="/membership">
            <Button variant="premium" size="xl">
              Start Trading Now
            </Button>
          </Link>
        </div>
      </section>
      </div>
    </>
  )
}
