import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { cancelPayPalSubscription } from '@/lib/paypal-subscriptions'
import { cancelSubscription, getUserSubscription } from '@/lib/subscription-manager'
import { z } from 'zod'
import { securityHeaders, createSafeErrorResponse } from '@/lib/security'

const cancelSubscriptionSchema = z.object({
  reason: z.string().optional().default('User requested cancellation')
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { 
        status: 401,
        headers: securityHeaders
      })
    }

    const body = await request.json()
    const { reason } = cancelSubscriptionSchema.parse(body)

    console.log('[Subscription Cancel] Cancelling subscription for user:', {
      userId: session.user.id,
      reason
    })

    // Get user's current subscription
    const subscription = await getUserSubscription(session.user.id)
    
    if (!subscription) {
      return NextResponse.json({
        success: false,
        message: 'No active subscription found'
      }, { 
        status: 404,
        headers: securityHeaders
      })
    }

    if (!subscription.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Subscription is not active'
      }, { 
        status: 400,
        headers: securityHeaders
      })
    }

    if (subscription.cancelAtPeriodEnd) {
      return NextResponse.json({
        success: false,
        message: 'Subscription is already scheduled for cancellation'
      }, { 
        status: 400,
        headers: securityHeaders
      })
    }

    try {
      // Cancel PayPal subscription
      if (subscription.paypalSubscriptionId) {
        console.log('[Subscription Cancel] Cancelling PayPal subscription:', subscription.paypalSubscriptionId)
        await cancelPayPalSubscription(subscription.paypalSubscriptionId, reason)
      }

      // Update our subscription record
      const cancelledSubscription = await cancelSubscription(session.user.id, reason)

      console.log('[Subscription Cancel] Subscription cancelled successfully:', {
        id: cancelledSubscription.id,
        cancelAtPeriodEnd: cancelledSubscription.cancelAtPeriodEnd,
        currentPeriodEnd: cancelledSubscription.currentPeriodEnd
      })

      return NextResponse.json({
        success: true,
        message: 'Subscription cancelled successfully. You will retain access until the end of your current billing period.',
        subscription: {
          id: cancelledSubscription.id,
          planType: cancelledSubscription.planType,
          status: cancelledSubscription.status,
          cancelAtPeriodEnd: cancelledSubscription.cancelAtPeriodEnd,
          cancelledAt: cancelledSubscription.cancelledAt,
          currentPeriodEnd: cancelledSubscription.currentPeriodEnd,
          accessUntil: cancelledSubscription.currentPeriodEnd
        }
      }, {
        headers: securityHeaders
      })

    } catch (paypalError: any) {
      console.error('[Subscription Cancel] PayPal cancellation failed:', paypalError)
      
      // Still update our record even if PayPal fails
      try {
        const cancelledSubscription = await cancelSubscription(session.user.id, `${reason} (PayPal cancellation failed: ${paypalError.message})`)
        
        return NextResponse.json({
          success: true,
          message: 'Subscription cancelled in our system. Please contact support if you continue to be charged.',
          warning: 'PayPal cancellation may have failed. Please verify in your PayPal account.',
          subscription: {
            id: cancelledSubscription.id,
            planType: cancelledSubscription.planType,
            status: cancelledSubscription.status,
            cancelAtPeriodEnd: cancelledSubscription.cancelAtPeriodEnd,
            cancelledAt: cancelledSubscription.cancelledAt,
            currentPeriodEnd: cancelledSubscription.currentPeriodEnd,
            accessUntil: cancelledSubscription.currentPeriodEnd
          }
        }, {
          headers: securityHeaders
        })
      } catch (dbError) {
        console.error('[Subscription Cancel] Database update also failed:', dbError)
        throw paypalError // Throw original PayPal error
      }
    }

  } catch (error: any) {
    console.error('[Subscription Cancel] Error cancelling subscription:', {
      error: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString()
    })

    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // PayPal specific errors
    if (error.message?.includes('PayPal') || error.message?.includes('subscription')) {
      return NextResponse.json({
        success: false,
        message: 'Failed to cancel subscription with PayPal. Please contact support.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      }, { 
        status: 503,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
