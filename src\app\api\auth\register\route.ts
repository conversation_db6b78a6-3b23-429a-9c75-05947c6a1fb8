import { NextRequest, NextResponse } from 'next/server'
import { createUser } from '@/lib/auth-utils'
import { sendWelcomeEmail } from '@/lib/email'
import { z } from 'zod'
import { validatePassword, rateLimit, sanitizeString, securityHeaders, createSafeErrorResponse } from '@/lib/security'

const registerSchema = z.object({
  email: z.string().email('Invalid email address').max(255, 'Email is too long'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name is too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name is too long'),
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for registration
    const rateLimitResult = await rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5 // 5 registration attempts per 15 minutes per IP
    })(request)

    if (!rateLimitResult.success) {
      return NextResponse.json({
        success: false,
        message: rateLimitResult.error
      }, {
        status: 429,
        headers: securityHeaders
      })
    }

    const body = await request.json()

    // Validate input
    const validatedData = registerSchema.parse(body)

    // Additional password validation
    const passwordValidation = validatePassword(validatedData.password)
    if (!passwordValidation.valid) {
      return NextResponse.json({
        success: false,
        message: 'Password does not meet security requirements',
        errors: passwordValidation.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // Sanitize input data
    const sanitizedData = {
      ...validatedData,
      firstName: sanitizeString(validatedData.firstName),
      lastName: sanitizeString(validatedData.lastName),
      email: validatedData.email.toLowerCase().trim()
    }

    // Create user
    const user = await createUser(sanitizedData)

    // Send welcome email
    try {
      await sendWelcomeEmail(user.email, {
        firstName: user.firstName,
        email: user.email,
        loginUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/auth/signin`
      })
      console.log(`Welcome email sent to: ${user.email}`)
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError)
      // Don't fail registration if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      data: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      }
    }, {
      status: 201,
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('Registration error:', error)

    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    if (error.code === 'P2002') {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
