import { prisma } from '@/lib/prisma'
import { generateSecureDownloadUrl } from '@/lib/cloudflare-r2'
import { checkDownloadLimit, getUserMembership, logDownload } from '@/lib/membership'
import { checkSubscriptionDownloadLimit, getUserSubscription, recordSubscriptionDownload } from '@/lib/subscription-manager'
import crypto from 'crypto'

export interface DownloadToken {
  id: string
  token: string
  productId: string
  userId: string
  downloadCount: number
  isActive: boolean
}

export interface DownloadResult {
  success: boolean
  downloadUrl?: string
  error?: string
}

/**
 * Create a secure download token for a user's purchased product
 */
export async function createDownloadToken(
  userId: string,
  productId: string
): Promise<DownloadToken> {
  try {
    // First check new subscription system
    const subscription = await getUserSubscription(userId)
    let hasAccess = false
    let downloadLimitCheck: any = null

    if (subscription && subscription.isActive) {
      // User has active subscription - check subscription download limits
      downloadLimitCheck = await checkSubscriptionDownloadLimit(userId)
      hasAccess = true

      console.log('[Download Manager] Subscription user access:', {
        userId,
        planType: subscription.planType,
        canDownload: downloadLimitCheck.canDownload,
        remainingDownloads: downloadLimitCheck.remainingDownloads
      })
    } else {
      // Fallback to legacy membership system
      const membership = await getUserMembership(userId)
      if (membership) {
        downloadLimitCheck = await checkDownloadLimit(userId)
        hasAccess = true

        console.log('[Download Manager] Legacy membership user access:', {
          userId,
          membershipType: membership.type,
          canDownload: downloadLimitCheck.canDownload,
          remainingDownloads: downloadLimitCheck.remainingDownloads
        })
      } else {
        // Check if user purchased this specific product
        const purchase = await prisma.purchase.findFirst({
          where: {
            userId,
            productId,
            status: 'COMPLETED'
          }
        })

        if (purchase) {
          hasAccess = true
          // For individual purchases, no daily limits
          downloadLimitCheck = {
            canDownload: true,
            remainingDownloads: 999,
            dailyLimit: 999,
            resetTime: new Date()
          }

          console.log('[Download Manager] Individual purchase access:', {
            userId,
            productId,
            purchaseId: purchase.id
          })
        }
      }
    }

    if (!hasAccess) {
      throw new Error('Active subscription or product purchase required for downloads')
    }

    if (!downloadLimitCheck.canDownload) {
      throw new Error(`Daily download limit reached. You can download ${downloadLimitCheck.remainingDownloads} more files today. Limit resets at ${downloadLimitCheck.resetTime.toLocaleString()}`)
    }

    // Check if there's an existing active token
    const existingToken = await prisma.downloadToken.findFirst({
      where: {
        userId,
        productId,
        isActive: true
      }
    })

    if (existingToken) {
      return existingToken
    }

    // Generate a secure token
    const token = crypto.randomBytes(32).toString('hex')

    // Create new download token
    const downloadToken = await prisma.downloadToken.create({
      data: {
        token,
        userId,
        productId,
        downloadCount: 0,
        isActive: true
      }
    })

    return downloadToken
  } catch (error) {
    console.error('Error creating download token:', error)
    throw error
  }
}

/**
 * Generate a secure download URL using a download token
 */
export async function generateDownloadUrl(
  token: string,
  userAgent?: string,
  ipAddress?: string
): Promise<DownloadResult> {
  try {
    // Find and validate the token
    const downloadToken = await prisma.downloadToken.findUnique({
      where: { token },
      include: {
        product: {
          include: {
            downloadFiles: true
          }
        },
        user: true
      }
    })

    if (!downloadToken) {
      return {
        success: false,
        error: 'Invalid download token'
      }
    }

    // Check if token is active
    if (!downloadToken.isActive) {
      return {
        success: false,
        error: 'Download token has been deactivated'
      }
    }

    // Check membership and daily download limits
    const downloadLimitCheck = await checkDownloadLimit(downloadToken.userId)
    if (!downloadLimitCheck.canDownload) {
      return {
        success: false,
        error: `Daily download limit reached. You have ${downloadLimitCheck.remainingDownloads} downloads remaining today. Limit resets at ${downloadLimitCheck.resetTime.toLocaleString()}`
      }
    }

    // Check if product has files
    if (!downloadToken.product.fileKey) {
      return {
        success: false,
        error: 'Product file not available. Please contact support for assistance.'
      }
    }

    // Determine file key and filename - check both direct product fields and ProductFile relations
    let fileKey: string | null = null
    let fileName: string | null = null

    // First, check if product has direct file fields
    if (downloadToken.product.fileKey && downloadToken.product.fileName) {
      fileName = downloadToken.product.fileName

      // Check if fileKey is actually a URL that needs parsing
      if (downloadToken.product.fileKey.includes('cloudflarestorage.com')) {
        // Extract key from R2 URL
        let urlToParse = downloadToken.product.fileKey

        // If it's a signed URL, extract the base URL first
        if (urlToParse.includes('?X-Amz-Algorithm=')) {
          const baseUrlMatch = urlToParse.match(/^([^?]+)/)
          if (baseUrlMatch) {
            urlToParse = baseUrlMatch[1]
          }
        }

        const match = urlToParse.match(/\.r2\.cloudflarestorage\.com\/(.+)$/)
        if (match) {
          fileKey = decodeURIComponent(match[1])
          console.log('Extracted file key from product.fileKey URL:', fileKey)
        } else {
          console.error('Failed to extract file key from product.fileKey URL:', downloadToken.product.fileKey)
          fileKey = downloadToken.product.fileKey // Fallback to original
        }
      } else {
        // It's already a key
        fileKey = downloadToken.product.fileKey
      }
    }
    // If not, check ProductFile relations
    else if (downloadToken.product.downloadFiles && downloadToken.product.downloadFiles.length > 0) {
      const productFile = downloadToken.product.downloadFiles[0] // Use first file
      fileName = productFile.fileName

      // Extract file key from fileUrl
      if (productFile.fileUrl.includes('cloudflarestorage.com')) {
        // For R2 URLs, extract the key after the bucket name
        // Handle both signed URLs and direct URLs
        let urlToParse = productFile.fileUrl

        // If it's a signed URL, extract the base URL first
        if (urlToParse.includes('?X-Amz-Algorithm=')) {
          const baseUrlMatch = urlToParse.match(/^([^?]+)/)
          if (baseUrlMatch) {
            urlToParse = baseUrlMatch[1]
          }
        }

        const match = urlToParse.match(/\.r2\.cloudflarestorage\.com\/(.+)$/)
        if (match) {
          fileKey = decodeURIComponent(match[1])
          console.log('Extracted file key from R2 URL:', fileKey)
        } else {
          console.error('Failed to extract file key from R2 URL:', productFile.fileUrl)
        }
      } else if (productFile.fileUrl.startsWith('http')) {
        // For other URLs, try to extract the path
        try {
          const url = new URL(productFile.fileUrl)
          fileKey = url.pathname.substring(1) // Remove leading slash
        } catch (e) {
          console.error('Failed to parse file URL:', productFile.fileUrl)
        }
      } else {
        // Assume it's already a key
        fileKey = productFile.fileUrl
      }
    }

    if (!fileKey || !fileName) {
      console.error('Download error - missing file info:', {
        productId: downloadToken.product.id,
        productName: downloadToken.product.name,
        hasDirectFileKey: !!downloadToken.product.fileKey,
        hasDirectFileName: !!downloadToken.product.fileName,
        downloadFilesCount: downloadToken.product.downloadFiles?.length || 0,
        downloadFiles: downloadToken.product.downloadFiles?.map(f => ({
          fileName: f.fileName,
          fileUrl: f.fileUrl
        }))
      })
      return {
        success: false,
        error: 'Product file not found. Please contact support for assistance.'
      }
    }

    console.log('Download info:', {
      productId: downloadToken.product.id,
      productName: downloadToken.product.name,
      fileKey,
      fileName,
      tokenId: downloadToken.id,
      originalFileKey: downloadToken.product.fileKey,
      downloadFilesCount: downloadToken.product.downloadFiles?.length || 0
    })

    // Generate secure download URL (valid for 1 hour)
    let downloadUrl: string
    try {
      downloadUrl = await generateSecureDownloadUrl(
        fileKey,
        fileName,
        3600 // 1 hour
      )
    } catch (error: any) {
      console.error('Error generating download URL:', error)
      return {
        success: false,
        error: 'Failed to generate download link. Please contact support for assistance.'
      }
    }

    // Record the download attempt
    await prisma.downloadToken.update({
      where: { id: downloadToken.id },
      data: {
        downloadCount: downloadToken.downloadCount + 1,
        lastDownloadAt: new Date()
      }
    })

    // Log the download
    await prisma.downloadLog.create({
      data: {
        tokenId: downloadToken.id,
        userId: downloadToken.userId,
        productId: downloadToken.productId,
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        downloadedAt: new Date()
      }
    })

    // Record subscription download if user has active subscription
    try {
      const subscription = await getUserSubscription(downloadToken.userId)
      if (subscription && subscription.isActive) {
        await recordSubscriptionDownload(downloadToken.userId, downloadToken.productId)
        console.log('[Download Manager] Subscription download recorded:', {
          userId: downloadToken.userId,
          productId: downloadToken.productId,
          planType: subscription.planType
        })
      }
    } catch (subscriptionError) {
      console.error('[Download Manager] Error recording subscription download:', subscriptionError)
      // Don't fail the download for subscription recording errors
    }

    return {
      success: true,
      downloadUrl
    }
  } catch (error) {
    console.error('Error generating download URL:', error)
    return {
      success: false,
      error: 'Failed to generate download URL'
    }
  }
}

/**
 * Get download status for a user's product
 */
export async function getDownloadStatus(
  userId: string,
  productId: string
): Promise<{
  hasAccess: boolean
  token?: string
  downloadCount: number
}> {
  try {
    let hasAccess = false

    // First check subscription access
    const subscription = await getUserSubscription(userId)
    if (subscription && subscription.isActive) {
      hasAccess = true
      console.log('[Download Manager] Subscription access granted:', {
        userId,
        productId,
        planType: subscription.planType
      })
    } else {
      // Check legacy membership
      const membership = await getUserMembership(userId)
      if (membership) {
        hasAccess = true
        console.log('[Download Manager] Legacy membership access granted:', {
          userId,
          productId,
          membershipType: membership.type
        })
      } else {
        // Check individual purchase
        const purchase = await prisma.purchase.findFirst({
          where: {
            userId,
            productId,
            status: 'COMPLETED'
          }
        })

        if (purchase) {
          hasAccess = true
          console.log('[Download Manager] Individual purchase access granted:', {
            userId,
            productId,
            purchaseId: purchase.id
          })
        }
      }
    }

    if (!hasAccess) {
      return {
        hasAccess: false,
        downloadCount: 0
      }
    }

    // Get the latest download token
    const downloadToken = await prisma.downloadToken.findFirst({
      where: {
        userId,
        productId,
        isActive: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!downloadToken) {
      return {
        hasAccess: true,
        downloadCount: 0
      }
    }

    return {
      hasAccess: true,
      token: downloadToken.token,
      downloadCount: downloadToken.downloadCount
    }
  } catch (error) {
    console.error('Error getting download status:', error)
    return {
      hasAccess: false,
      downloadCount: 0
    }
  }
}

/**
 * Revoke a download token
 */
export async function revokeDownloadToken(tokenId: string): Promise<boolean> {
  try {
    await prisma.downloadToken.update({
      where: { id: tokenId },
      data: { isActive: false }
    })
    return true
  } catch (error) {
    console.error('Error revoking download token:', error)
    return false
  }
}

/**
 * Clean up inactive tokens (no longer needed since tokens don't expire)
 */
export async function cleanupInactiveTokens(): Promise<number> {
  try {
    // This function is kept for backward compatibility but doesn't do anything
    // since tokens no longer expire
    return 0
  } catch (error) {
    console.error('Error cleaning up tokens:', error)
    return 0
  }
}
