# PayPal Webhook Setup Guide for Local Development

## Overview
This guide will help you set up PayPal webhooks for local development using ngrok to expose your local server to the internet.

## Prerequisites
- PayPal Developer Account
- Node.js application running locally
- ngrok installed (for exposing local server)

## Step 1: Install and Setup ngrok

### Install ngrok
```bash
# Download from https://ngrok.com/download
# Or install via npm
npm install -g ngrok

# Or install via homebrew (macOS)
brew install ngrok
```

### Create ngrok account and get auth token
1. Go to https://ngrok.com/signup
2. Sign up for a free account
3. Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken
4. Configure ngrok with your auth token:
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

## Step 2: Expose Your Local Server

### Start your local development server
```bash
npm run dev
# Your app should be running on http://localhost:3000
```

### In a new terminal, start ngrok
```bash
ngrok http 3000
```

You'll see output like:
```
Session Status                online
Account                       <EMAIL>
Version                       3.x.x
Region                        United States (us)
Latency                       -
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:3000
```

**Important:** Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`) - this is your public webhook URL.

## Step 3: Configure PayPal Developer Dashboard

### Access PayPal Developer Dashboard
1. Go to https://developer.paypal.com/
2. Log in with your PayPal account
3. Go to "My Apps & Credentials"

### Create or Edit Your App
1. If you don't have an app, click "Create App"
2. If you have an app, click on it to edit
3. Make sure you're in "Sandbox" mode for development

### Configure Webhooks
1. In your app settings, scroll down to "Webhooks"
2. Click "Add Webhook"
3. Enter your webhook URL: `https://your-ngrok-url.ngrok.io/api/paypal/webhook`
4. Select the following event types:
   - `CHECKOUT.ORDER.APPROVED`
   - `PAYMENT.CAPTURE.COMPLETED`
   - `PAYMENT.CAPTURE.DENIED`
   - `PAYMENT.CAPTURE.DECLINED`

### Get Webhook ID
1. After creating the webhook, you'll see a Webhook ID
2. Copy this ID - you'll need it for your environment variables

## Step 4: Update Environment Variables

Update your `.env` file with the webhook ID:

```env
# PayPal Configuration
PAYPAL_CLIENT_ID=your-sandbox-client-id
PAYPAL_CLIENT_SECRET=your-sandbox-client-secret
PAYPAL_WEBHOOK_ID=your-webhook-id-from-step-3

# Update your site URL to use ngrok
SITE_URL=https://your-ngrok-url.ngrok.io
NEXTAUTH_URL=https://your-ngrok-url.ngrok.io
```

## Step 5: Test Webhook Delivery

### Test PayPal Integration
1. Restart your development server to load new environment variables
2. Go to your ngrok URL: `https://your-ngrok-url.ngrok.io`
3. Try to make a test purchase using PayPal
4. Check your server logs for webhook events

### Monitor Webhook Events
1. In PayPal Developer Dashboard, go to your webhook settings
2. Click on "Webhook events" to see delivery attempts
3. You can also use ngrok's web interface at http://127.0.0.1:4040 to inspect requests

### Debug Webhook Issues
If webhooks aren't working:

1. **Check ngrok is running**: Make sure ngrok is still active
2. **Verify webhook URL**: Ensure the URL in PayPal matches your ngrok URL
3. **Check server logs**: Look for webhook processing logs
4. **Test webhook endpoint**: Visit `https://your-ngrok-url.ngrok.io/api/paypal/webhook` directly
5. **Verify environment variables**: Ensure PAYPAL_WEBHOOK_ID is set correctly

## Step 6: Production Deployment

When deploying to production:

### Update PayPal App Settings
1. Switch to "Live" mode in PayPal Developer Dashboard
2. Update webhook URL to your production domain
3. Update environment variables with live credentials

### Environment Variables for Production
```env
# PayPal Configuration (Live)
PAYPAL_CLIENT_ID=your-live-client-id
PAYPAL_CLIENT_SECRET=your-live-client-secret
PAYPAL_WEBHOOK_ID=your-live-webhook-id

# Production URLs
SITE_URL=https://yourdomain.com
NEXTAUTH_URL=https://yourdomain.com
```

## Troubleshooting

### Common Issues

#### 1. Webhook URL Not Reachable
- **Problem**: PayPal can't reach your webhook URL
- **Solution**: Ensure ngrok is running and URL is correct

#### 2. Webhook Signature Verification Failed
- **Problem**: Webhook signature doesn't match
- **Solution**: Verify PAYPAL_WEBHOOK_ID is correct

#### 3. ngrok Session Expired
- **Problem**: Free ngrok sessions expire after 8 hours
- **Solution**: Restart ngrok and update webhook URL in PayPal

#### 4. Environment Variables Not Loading
- **Problem**: New environment variables not recognized
- **Solution**: Restart your development server

### Testing Commands

Test webhook endpoint directly:
```bash
curl -X POST https://your-ngrok-url.ngrok.io/api/paypal/webhook \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

Check if PayPal can reach your webhook:
```bash
# This should return a 405 Method Not Allowed (expected for GET)
curl https://your-ngrok-url.ngrok.io/api/paypal/webhook
```

## Security Notes

### Development Security
- ngrok URLs are temporary and change each restart
- Don't commit ngrok URLs to version control
- Use environment variables for all sensitive data

### Production Security
- Always use HTTPS in production
- Verify webhook signatures
- Implement proper error handling
- Monitor webhook delivery failures

## Alternative Solutions

### For Team Development
If multiple developers need webhook access:

1. **Use a shared development server**: Deploy to a staging environment
2. **Use webhook forwarding services**: Services like webhook.site for testing
3. **Mock webhook events**: Create test endpoints that simulate PayPal webhooks

### For CI/CD Testing
```bash
# Use PayPal's webhook simulator for automated testing
# This is available in PayPal's developer tools
```

## Support Resources

- [PayPal Webhooks Documentation](https://developer.paypal.com/api/rest/webhooks/)
- [ngrok Documentation](https://ngrok.com/docs)
- [PayPal Developer Support](https://developer.paypal.com/support/)

## Quick Reference

### Essential Commands
```bash
# Start ngrok
ngrok http 3000

# Test webhook endpoint
curl -X POST https://your-ngrok-url.ngrok.io/api/paypal/webhook

# Check ngrok web interface
open http://127.0.0.1:4040
```

### Important URLs
- PayPal Developer Dashboard: https://developer.paypal.com/
- ngrok Dashboard: https://dashboard.ngrok.com/
- Webhook URL Format: `https://your-ngrok-url.ngrok.io/api/paypal/webhook`

Remember to update your webhook URL in PayPal Developer Dashboard whenever you restart ngrok, as the URL changes each time!
