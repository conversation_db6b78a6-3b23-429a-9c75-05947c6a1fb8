// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String?   @unique
  firstName     String?
  lastName      String?
  avatar        String?
  emailVerified DateTime?
  password      String?
  role          UserRole  @default(CUSTOMER)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  orders        Order[]
  purchases     Purchase[]
  downloads     Download[]
  downloadTokens DownloadToken[]
  downloadLogs  DownloadLog[]
  reviews       Review[]
  membership    Membership?
  subscription  Subscription?
  dailyDownloadLimits DailyDownloadLimit[]
  cart          CartItem[]
  wishlist      WishlistItem[]
  couponUsages  CouponUsage[]
  reviewedManualPayments ManualPayment[]
  supportMessages SupportMessage[]
  adminResponses SupportResponse[] @relation("AdminResponses")

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  image       String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products    Product[]

  @@map("categories")
}

model Product {
  id              String        @id @default(cuid())
  name            String
  slug            String        @unique
  description     String?
  shortDescription String?
  price           Decimal       @db.Decimal(10, 2)
  originalPrice   Decimal?      @db.Decimal(10, 2)
  isOnSale        Boolean       @default(false)
  salePrice       Decimal?      @db.Decimal(10, 2)
  images          String[]
  downloadFiles   ProductFile[]
  categoryId      String
  status          ProductStatus @default(DRAFT)
  featured        Boolean       @default(false)
  fileKey         String?       // R2 storage key
  fileName        String?       // Original filename
  fileSize        BigInt?       // File size in bytes
  tags            String[]
  metaTitle       String?
  metaDescription String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  category       Category        @relation(fields: [categoryId], references: [id])
  orderItems     OrderItem[]
  purchases      Purchase[]
  reviews        Review[]
  downloads      Download[]
  downloadTokens DownloadToken[]
  downloadLogs   DownloadLog[]
  cartItems      CartItem[]
  wishlistItems  WishlistItem[]

  @@map("products")
}

model ProductFile {
  id        String   @id @default(cuid())
  productId String
  fileName  String
  fileUrl   String
  fileSize  BigInt
  fileType  String
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_files")
}

// Coupon Management
model Coupon {
  id                String      @id @default(cuid())
  code              String      @unique
  name              String
  description       String?
  type              CouponType
  value             Decimal     @db.Decimal(10, 2) // Amount or percentage
  minimumAmount     Decimal?    @db.Decimal(10, 2) // Minimum order amount
  maximumDiscount   Decimal?    @db.Decimal(10, 2) // Maximum discount for percentage coupons
  usageLimit        Int?        // Total usage limit
  usageCount        Int         @default(0)
  userUsageLimit    Int?        // Per user usage limit
  isActive          Boolean     @default(true)
  startsAt          DateTime?
  expiresAt         DateTime?
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  orders            Order[]
  couponUsages      CouponUsage[]

  @@map("coupons")
}

model CouponUsage {
  id        String   @id @default(cuid())
  couponId  String
  userId    String?  // Optional for guest users
  orderId   String
  usedAt    DateTime @default(now())

  // Relations
  coupon    Coupon   @relation(fields: [couponId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@unique([couponId, orderId])
  @@map("coupon_usages")
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  userId          String?     // Made optional for guest checkout
  email           String
  firstName       String
  lastName        String
  status          OrderStatus @default(PENDING)
  subtotalAmount  Decimal     @db.Decimal(10, 2) // Amount before discount
  discountAmount  Decimal     @db.Decimal(10, 2) @default(0) // Discount applied
  totalAmount     Decimal     @db.Decimal(10, 2) // Final amount after discount
  couponId        String?     // Applied coupon
  couponCode      String?     // Store coupon code for reference
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  paymentIntentId String?
  paypalOrderId   String?
  paymentProofUrl String?
  paymentProofFileName String?
  manualPaymentAmount Decimal? @db.Decimal(10, 2)
  manualPaymentCurrency String?
  manualPaymentAddress String?
  adminNotes      String?
  notes           String?
  metadata        Json?       // Store PayPal and other payment-related data
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user         User?         @relation(fields: [userId], references: [id])
  coupon       Coupon?       @relation(fields: [couponId], references: [id])
  items        OrderItem[]
  payments     Payment[]
  purchases    Purchase[]
  couponUsages CouponUsage[]
  manualPayments ManualPayment[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int     @default(1)
  price     Decimal @db.Decimal(10, 2)
  createdAt DateTime @default(now())

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Payment Management
model Payment {
  id              String        @id @default(cuid())
  orderId         String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  paymentMethod   String
  transactionId   String?
  paymentIntentId String?
  metadata        Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id])

  @@map("payments")
}

// Purchase Tracking (for download access)
model Purchase {
  id        String        @id @default(cuid())
  userId    String
  productId String
  orderId   String?
  status    PurchaseStatus @default(PENDING)
  price     Decimal       @db.Decimal(10, 2)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  order   Order?  @relation(fields: [orderId], references: [id])

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@map("purchases")
}

// Download Management
model Download {
  id           String   @id @default(cuid())
  userId       String
  productId    String
  downloadUrl  String
  downloadCount Int     @default(0)
  lastDownloadAt DateTime?
  createdAt    DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@map("downloads")
}

// Secure Download Tokens
model DownloadToken {
  id            String   @id @default(cuid())
  token         String   @unique
  userId        String
  productId     String
  downloadCount Int      @default(0)
  isActive      Boolean  @default(true)
  lastDownloadAt DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  product      Product       @relation(fields: [productId], references: [id], onDelete: Cascade)
  downloadLogs DownloadLog[]

  @@index([userId, productId])
  @@index([token])
  @@map("download_tokens")
}

// Download Activity Logs
model DownloadLog {
  id          String   @id @default(cuid())
  tokenId     String
  userId      String
  productId   String
  ipAddress   String
  userAgent   String
  downloadedAt DateTime @default(now())

  // Relations
  token   DownloadToken @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product       @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([productId])
  @@index([downloadedAt])
  @@map("download_logs")
}

// Shopping Cart
model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

// Wishlist
model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Reviews
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      @db.SmallInt
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
  @@map("reviews")
}

// Legacy Membership System (DEPRECATED - Use Subscription model instead)
// This model is kept for backward compatibility and data migration
// TODO: Remove after all data is migrated to Subscription model
model Membership {
  id              String           @id @default(cuid())
  userId          String           @unique
  type            MembershipType
  status          MembershipStatus @default(ACTIVE)
  startDate       DateTime
  endDate         DateTime?
  autoRenew       Boolean          @default(false)
  stripeCustomerId String?
  subscriptionId  String?
  paymentMethod   String?          // Added for compatibility
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("memberships")
}

// New Subscription System
model Subscription {
  id                   String              @id @default(cuid())
  userId               String              @unique
  planType             MembershipType      // BRONZE, SILVER, GOLD
  status               SubscriptionStatus  @default(PENDING)
  paypalSubscriptionId String?             // PayPal subscription ID
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean             @default(false)
  cancelledAt          DateTime?
  trialEnd             DateTime?
  nextBillingDate      DateTime?
  billingCycleSequence Int                 @default(1)
  metadata             Json?               // PayPal webhook data
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt

  // Relations
  user                 User                @relation(fields: [userId], references: [id])
  payments             SubscriptionPayment[]

  @@map("subscriptions")
}

model SubscriptionPayment {
  id              String            @id @default(cuid())
  subscriptionId  String
  paypalPaymentId String?           // PayPal payment ID
  amount          Decimal           @db.Decimal(10, 2)
  currency        String            @default("USD")
  status          PaymentStatus     @default(PENDING)
  billingPeriod   String            // "2024-01", "2024-02", etc.
  paidAt          DateTime?
  failureReason   String?
  metadata        Json?             // PayPal payment data
  createdAt       DateTime          @default(now())

  // Relations
  subscription    Subscription      @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("subscription_payments")
}

// Enhanced download tracking
model DailyDownloadLimit {
  id              String         @id @default(cuid())
  userId          String
  date            DateTime       // Date (YYYY-MM-DD)
  downloadCount   Int            @default(0)
  membershipType  MembershipType
  dailyLimit      Int
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  user            User           @relation(fields: [userId], references: [id])

  @@unique([userId, date])
  @@map("daily_download_limits")
}

// Enums
enum UserRole {
  ADMIN
  CUSTOMER
}

enum ProductStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum OrderStatus {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED
}

enum MembershipType {
  BRONZE
  SILVER
  GOLD
}

enum MembershipStatus {
  ACTIVE
  EXPIRED
  CANCELLED
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  CANCELLED
  EXPIRED
  SUSPENDED
  PAST_DUE
}

enum CouponType {
  FIXED_AMOUNT    // Fixed dollar amount off
  PERCENTAGE      // Percentage off
  FREE_SHIPPING   // Free shipping (for future use)
}

// Support Messages
model SupportMessage {
  id          String              @id @default(cuid())
  name        String
  email       String
  subject     String
  message     String              @db.Text
  status      SupportMessageStatus @default(OPEN)
  priority    SupportMessagePriority @default(NORMAL)
  userId      String?             // Optional - for registered users
  isGuest     Boolean             @default(true)
  adminNotes  String?             @db.Text
  respondedAt DateTime?
  respondedBy String?             // Admin user ID who responded
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Relations
  user        User?               @relation(fields: [userId], references: [id], onDelete: SetNull)
  responses   SupportResponse[]

  @@index([status])
  @@index([createdAt])
  @@index([userId])
  @@map("support_messages")
}

model SupportResponse {
  id        String   @id @default(cuid())
  messageId String
  content   String   @db.Text
  isAdmin   Boolean  @default(false)
  adminId   String?  // Admin user ID who sent the response
  createdAt DateTime @default(now())

  // Relations
  message   SupportMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  admin     User?          @relation("AdminResponses", fields: [adminId], references: [id], onDelete: SetNull)

  @@index([messageId])
  @@index([createdAt])
  @@map("support_responses")
}

// Email Management
model EmailTemplate {
  id          String            @id @default(cuid())
  name        String
  subject     String
  htmlContent String            @db.Text
  textContent String?           @db.Text
  type        EmailTemplateType
  isActive    Boolean           @default(true)
  variables   Json?             // Store template variables as JSON
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  emailLogs   EmailLog[]

  @@map("email_templates")
}

model EmailLog {
  id           String           @id @default(cuid())
  templateId   String?
  to           String
  from         String
  subject      String
  htmlContent  String?          @db.Text
  textContent  String?          @db.Text
  status       EmailStatus      @default(PENDING)
  sentAt       DateTime?
  deliveredAt  DateTime?
  openedAt     DateTime?
  clickedAt    DateTime?
  bouncedAt    DateTime?
  failedAt     DateTime?
  errorMessage String?
  metadata     Json?            // Store additional data as JSON
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relations
  template     EmailTemplate?   @relation(fields: [templateId], references: [id])

  @@map("email_logs")
}

enum EmailTemplateType {
  WELCOME
  ORDER_CONFIRMATION
  PASSWORD_RESET
  SUPPORT_CONFIRMATION
  SUPPORT_REPLY
  CUSTOM
}

enum SupportMessageStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum SupportMessagePriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum EmailStatus {
  PENDING
  SENT
  DELIVERED
  OPENED
  CLICKED
  BOUNCED
  FAILED
}

// Manual Payment Management
model ManualPayment {
  id              String   @id @default(cuid())
  orderId         String
  paymentMethod   String
  amount          Decimal  @db.Decimal(10, 2)
  currency        String   @default("USDT")
  walletAddress   String
  transactionHash String?
  proofImageUrl   String
  proofFileName   String
  status          ManualPaymentStatus @default(PENDING)
  submittedAt     DateTime @default(now())
  reviewedAt      DateTime?
  reviewedBy      String?
  adminNotes      String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  order    Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
  reviewer User? @relation(fields: [reviewedBy], references: [id])

  @@map("manual_payments")
}

model PaymentMethod {
  id                 String   @id @default(cuid())
  name               String
  type               PaymentMethodType
  currency           String
  walletAddress      String?
  qrCodeUrl          String?
  discountPercentage Decimal? @db.Decimal(5, 2) @default(0)
  isActive           Boolean  @default(true)
  instructions       String?
  minimumAmount      Decimal? @db.Decimal(10, 2)
  maximumAmount      Decimal? @db.Decimal(10, 2)
  processingTime     String?
  sortOrder          Int      @default(0)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("payment_methods")
}

enum ManualPaymentStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSING

  @@map("ManualPaymentStatus")
}

enum PaymentMethodType {
  STRIPE
  PAYPAL
  MANUAL

  @@map("PaymentMethodType")
}
