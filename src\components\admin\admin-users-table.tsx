'use client'

import { useEffect, useState } from 'react'
import {
  PencilIcon,
  TrashIcon,
  UserCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { DeleteConfirmationModal } from '@/components/ui/delete-confirmation-modal'
import { EditUserModal } from '@/components/admin/edit-user-modal'
import { UserDetailsModal } from '@/components/admin/user-details-modal'
import { User } from '@prisma/client'
import { toast } from 'react-hot-toast'

export function AdminUsersTable() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean
    user: User | null
    loading: boolean
  }>({
    isOpen: false,
    user: null,
    loading: false
  })
  const [editModal, setEditModal] = useState<{
    isOpen: boolean
    user: User | null
  }>({
    isOpen: false,
    user: null
  })
  const [viewModal, setViewModal] = useState<{
    isOpen: boolean
    user: User | null
  }>({
    isOpen: false,
    user: null
  })

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch('/api/admin/users')
        if (response.ok) {
          const data = await response.json()
          setUsers(data.data || [])
        }
      } catch (error) {
        console.error('Error fetching users:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: newRole }),
      })

      if (response.ok) {
        const data = await response.json()
        setUsers(users.map(user =>
          user.id === userId ? data.data : user
        ))
        toast.success('User role updated successfully!')
      } else {
        const data = await response.json()
        toast.error(data.message || 'Failed to update user role')
      }
    } catch (error) {
      console.error('Error updating user role:', error)
      toast.error('Failed to update user role')
    }
  }

  const handleEditUser = (user: User) => {
    setEditModal({
      isOpen: true,
      user
    })
  }

  const handleViewUser = (user: User) => {
    setViewModal({
      isOpen: true,
      user
    })
  }

  const handleDeleteUser = (user: User) => {
    setDeleteModal({
      isOpen: true,
      user,
      loading: false
    })
  }

  const confirmDeleteUser = async () => {
    if (!deleteModal.user) return

    setDeleteModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/users/${deleteModal.user.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setUsers(users.filter(user => user.id !== deleteModal.user!.id))
        toast.success('User deleted successfully!')
        setDeleteModal({ isOpen: false, user: null, loading: false })
      } else {
        const data = await response.json()
        toast.error(data.message || 'Failed to delete user')
        setDeleteModal(prev => ({ ...prev, loading: false }))
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      toast.error('Failed to delete user')
      setDeleteModal(prev => ({ ...prev, loading: false }))
    }
  }

  const handleUserUpdated = (updatedUser: User) => {
    setUsers(users.map(user =>
      user.id === updatedUser.id ? updatedUser : user
    ))
    setEditModal({ isOpen: false, user: null })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden">
      <div className="px-6 py-4 border-b border-white/10">
        <h2 className="text-lg font-medium text-white">All Users</h2>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-700">
          <thead className="bg-gray-800/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Joined
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-700">
            {users.map((user) => (
              <tr key={user.id} className="hover:bg-white/5">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <UserCircleIcon className="h-10 w-10 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-white">
                        {user.firstName} {user.lastName}
                      </div>
                      <div className="text-sm text-gray-400">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={user.role}
                    onChange={(e) => handleRoleChange(user.id, e.target.value)}
                    className="bg-gray-800 border border-gray-600 text-white text-sm rounded-md px-2 py-1"
                  >
                    <option value="USER">User</option>
                    <option value="ADMIN">Admin</option>
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  {new Date(user.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewUser(user)}
                      className="text-green-400 hover:text-green-300"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditUser(user)}
                      className="text-blue-400 hover:text-blue-300"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteUser(user)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {users.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400">No users found.</p>
        </div>
      )}

      {/* View User Modal */}
      <UserDetailsModal
        isOpen={viewModal.isOpen}
        onClose={() => setViewModal({ isOpen: false, user: null })}
        user={viewModal.user}
      />

      {/* Edit User Modal */}
      <EditUserModal
        isOpen={editModal.isOpen}
        onClose={() => setEditModal({ isOpen: false, user: null })}
        onUserUpdated={handleUserUpdated}
        user={editModal.user}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, user: null, loading: false })}
        onConfirm={confirmDeleteUser}
        title="Delete User"
        message="Are you sure you want to delete this user? This action cannot be undone and will permanently remove the user and all associated data."
        itemName={deleteModal.user ? `${deleteModal.user.firstName} ${deleteModal.user.lastName} (${deleteModal.user.email})` : ''}
        loading={deleteModal.loading}
      />
    </div>
  )
}
