import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { capturePayPalOrder } from '@/lib/paypal'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { securityHeaders, createSafeErrorResponse } from '@/lib/security'
import { sendOrderConfirmationEmail } from '@/lib/email'

const captureOrderSchema = z.object({
  orderID: z.string().min(1, 'Order ID is required').optional(),
  orderId: z.string().min(1, 'Order ID is required').optional(),
  customerInfo: z.object({
    email: z.string().email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1)
  }).optional(),
  items: z.array(z.any()).optional(),
  couponCode: z.string().optional()
}).refine(data => data.orderID || data.orderId, {
  message: "Either orderID or orderId is required"
})

export async function POST(request: NextRequest) {
  try {
    console.log('[PayPal API] Capture order request received')

    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      console.error('[PayPal API] No authenticated user found')
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, {
        status: 401,
        headers: securityHeaders
      })
    }

    const body = await request.json()
    console.log('[PayPal API] Capture request body:', { orderID: body.orderID, orderId: body.orderId, userId: session.user.id })

    const validatedData = captureOrderSchema.parse(body)
    const orderId = validatedData.orderID || validatedData.orderId

    // Verify order belongs to user (orderId is the PayPal order ID)
    const order = await prisma.order.findFirst({
      where: {
        userId: session.user.id,
        paypalOrderId: orderId,
        status: 'PENDING'
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        user: true
      }
    })

    console.log('[PayPal API] Order lookup result:', {
      found: !!order,
      orderId: order?.id,
      orderNumber: order?.orderNumber,
      status: order?.status
    })

    if (!order) {
      console.error('[PayPal API] Order not found:', { orderId, userId: session.user.id })
      return NextResponse.json({
        success: false,
        message: 'Order not found or already processed'
      }, {
        status: 404,
        headers: securityHeaders
      })
    }

    // Capture PayPal payment
    console.log('[PayPal API] Capturing PayPal payment:', orderId)
    const captureResult = await capturePayPalOrder(orderId)
    console.log('[PayPal API] Capture result:', {
      id: captureResult.id,
      status: captureResult.status,
      captureCount: captureResult.purchase_units?.[0]?.payments?.captures?.length
    })

    // Check if payment was successful or pending
    const capture = captureResult.purchase_units?.[0]?.payments?.captures?.[0]
    if (!capture) {
      console.error('[PayPal API] No capture found in response:', {
        orderStatus: captureResult.status,
        purchaseUnits: captureResult.purchase_units?.length
      })
      return NextResponse.json({
        success: false,
        message: 'Payment capture failed - no capture found'
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // Handle different capture statuses
    if (capture.status === 'PENDING') {
      console.log('[PayPal API] Payment capture is pending:', {
        captureId: capture.id,
        reason: capture.status_details?.reason,
        description: capture.status_details?.description
      })

      // For pending captures, we'll update the order but mark it as processing
      // The webhook will complete it when PayPal processes the payment
      const updatedOrder = await prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'PROCESSING',
          paymentStatus: 'PENDING',
          paymentIntentId: capture.id,
          metadata: {
            ...order.metadata as any,
            paypalCaptureData: captureResult,
            captureStatus: 'PENDING',
            pendingReason: capture.status_details?.reason
          }
        },
        include: {
          items: {
            include: {
              product: true
            }
          },
          user: true
        }
      })

      return NextResponse.json({
        success: true,
        pending: true,
        message: 'Payment is being processed. You will receive confirmation once completed.',
        order: {
          id: updatedOrder.id,
          orderNumber: updatedOrder.orderNumber,
          status: updatedOrder.status,
          paymentStatus: updatedOrder.paymentStatus,
          total: Number(updatedOrder.totalAmount),
          captureId: capture.id,
          items: updatedOrder.items.map(item => ({
            id: item.id,
            productId: item.productId,
            productName: item.product.name,
            quantity: item.quantity,
            price: Number(item.price)
          }))
        }
      }, {
        headers: securityHeaders
      })
    } else if (capture.status !== 'COMPLETED') {
      console.error('[PayPal API] Payment capture failed:', {
        captureStatus: capture.status,
        captureId: capture.id,
        orderStatus: captureResult.status,
        statusDetails: capture.status_details
      })
      return NextResponse.json({
        success: false,
        message: `Payment capture failed with status: ${capture.status}`
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    console.log('[PayPal API] Payment captured successfully:', {
      captureId: capture.id,
      amount: capture.amount?.value
    })

    // Update order status
    const updatedOrder = await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'COMPLETED',
        paymentStatus: 'COMPLETED',
        paymentIntentId: capture.id,
        metadata: {
          ...order.metadata as any,
          paypalCaptureData: captureResult
        }
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        user: true
      }
    })

    // Create purchases for each item (use upsert to handle duplicates)
    const purchases = await Promise.all(
      updatedOrder.items.map(async (item) => {
          return prisma.purchase.upsert({
            where: {
              userId_productId: {
                userId: session.user.id,
                productId: item.productId
              }
            },
            update: {
              orderId: updatedOrder.id,
              price: item.price,
              quantity: item.quantity,
              status: 'COMPLETED',
              updatedAt: new Date()
            },
            create: {
              userId: session.user.id,
              productId: item.productId,
              orderId: updatedOrder.id,
              price: item.price,
              quantity: item.quantity,
              status: 'COMPLETED'
            }
          })
        })
      )

      console.log('[PayPal API] Purchases created:', purchases.length)

    // Send order confirmation email
    try {
      await sendOrderConfirmationEmail({
        to: updatedOrder.email,
        customerName: `${updatedOrder.firstName} ${updatedOrder.lastName}`,
        orderNumber: updatedOrder.orderNumber,
        items: updatedOrder.items.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: Number(item.price)
        })),
        subtotal: Number(updatedOrder.subtotalAmount),
        discount: Number(updatedOrder.discountAmount || 0),
        total: Number(updatedOrder.totalAmount),
        paymentMethod: 'PayPal'
      })
    } catch (emailError) {
      console.error('Failed to send order confirmation email:', emailError)
      // Don't fail the order if email fails
    }

    console.log('[PayPal API] Order completed successfully:', {
      orderId: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      total: updatedOrder.totalAmount,
      captureId: capture.id
    })

    return NextResponse.json({
      success: true,
      order: {
        id: updatedOrder.id,
        orderNumber: updatedOrder.orderNumber,
        status: updatedOrder.status,
        paymentStatus: updatedOrder.paymentStatus,
        total: Number(updatedOrder.totalAmount),
        captureId: capture.id,
        items: updatedOrder.items.map(item => ({
          id: item.id,
          productId: item.productId,
          productName: item.product.name,
          quantity: item.quantity,
          price: Number(item.price)
        }))
      }
    }, {
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('[PayPal API] Error capturing order:', {
      error: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      requestBody: body
    })

    if (error.name === 'ZodError') {
      console.error('[PayPal API] Validation error:', error.errors)
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // PayPal specific errors
    if (error.message?.includes('PAYPAL') || error.message?.includes('capture')) {
      console.error('[PayPal API] PayPal capture error:', error)
      return NextResponse.json({
        success: false,
        message: 'Payment capture failed. Please contact support.'
      }, {
        status: 503,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
