import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { ProductCard } from '@/components/products/product-card'
import { BreadcrumbStructuredData } from '@/components/seo/structured-data'

interface CategoryPageProps {
  params: {
    slug: string
  }
}

async function getCategory(slug: string) {
  const category = await prisma.category.findUnique({
    where: { 
      slug,
      isActive: true
    },
    include: {
      products: {
        where: { status: 'PUBLISHED' },
        include: {
          category: true,
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: [
          { featured: 'desc' },
          { createdAt: 'desc' }
        ]
      },
      _count: {
        select: {
          products: {
            where: { status: 'PUBLISHED' }
          }
        }
      }
    }
  })

  if (!category) return null

  // Calculate average ratings for products
  const productsWithRatings = await Promise.all(
    category.products.map(async (product) => {
      const avgRating = await prisma.review.aggregate({
        where: { productId: product.id },
        _avg: { rating: true }
      })

      return {
        ...product,
        averageRating: avgRating._avg.rating || 0,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        salePrice: product.salePrice ? Number(product.salePrice) : null,
        fileSize: product.fileSize ? product.fileSize.toString() : null,
      }
    })
  )

  return {
    ...category,
    products: productsWithRatings
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = await getCategory(params.slug)
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  if (!category) {
    return {
      title: 'Category Not Found - Forex Bot Zone',
      description: 'The requested category could not be found.',
    }
  }

  const productCount = category._count.products

  return {
    title: `${category.name} - Premium Forex Tools | Forex Bot Zone`,
    description: `Browse our collection of ${productCount} premium ${category.name.toLowerCase()} for forex trading. Professional MT4/MT5 tools at special prices.`,
    keywords: [
      category.name.toLowerCase(),
      'forex',
      'trading',
      'expert advisor',
      'indicators',
      'MT4',
      'MT5',
      'automated trading',
      'forex bot'
    ].join(', '),
    alternates: {
      canonical: `/categories/${category.slug}`,
    },
    openGraph: {
      title: `${category.name} - Forex Bot Zone`,
      description: `Browse our collection of premium ${category.name.toLowerCase()} for forex trading.`,
      type: 'website',
      url: `/categories/${category.slug}`,
      siteName: 'Forex Bot Zone',
    },
    twitter: {
      card: 'summary',
      title: `${category.name} - Forex Bot Zone`,
      description: `Browse our collection of premium ${category.name.toLowerCase()} for forex trading.`,
    },
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const category = await getCategory(params.slug)
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  if (!category) {
    notFound()
  }

  // Breadcrumb data
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Categories', url: '/categories' },
    { name: category.name, url: `/categories/${category.slug}` }
  ]

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} baseUrl={baseUrl} />
      
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {category.name}
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              {category.description || `Discover our premium collection of ${category.name.toLowerCase()} designed for professional forex traders.`}
            </p>
            <div className="mt-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                {category._count.products} Products Available
              </span>
            </div>
          </div>

          {/* Products Grid */}
          {category.products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {category.products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="text-gray-400 text-lg mb-4">
                No products available in this category yet.
              </div>
              <p className="text-gray-500">
                Check back soon for new additions to our {category.name.toLowerCase()} collection.
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

// Generate static params for all active categories
export async function generateStaticParams() {
  const categories = await prisma.category.findMany({
    where: { isActive: true },
    select: { slug: true }
  })

  return categories.map((category) => ({
    slug: category.slug,
  }))
}
