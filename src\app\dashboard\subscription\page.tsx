'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CrownIcon, CalendarIcon, DownloadIcon, ClockIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import { toast } from 'react-hot-toast'

interface Subscription {
  id: string
  planType: 'BRONZE' | 'SILVER' | 'GOLD'
  status: 'PENDING' | 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'SUSPENDED' | 'PAST_DUE'
  isActive: boolean
  startDate: string
  endDate?: string
  nextBillingDate?: string
  paypalSubscriptionId?: string
  createdAt: string
  updatedAt: string
}

interface SubscriptionHistory {
  id: string
  planType: 'BRONZE' | 'SILVER' | 'GOLD'
  status: string
  startDate: string
  endDate?: string
  paymentMethod: string
  createdAt: string
  updatedAt: string
}

const subscriptionColors = {
  BRONZE: 'from-amber-500 to-orange-600',
  SILVER: 'from-gray-400 to-gray-600',
  GOLD: 'from-yellow-400 to-yellow-600'
}

const subscriptionBadgeColors = {
  BRONZE: 'bg-amber-500/20 text-amber-400 border-amber-500/30',
  SILVER: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
  GOLD: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
}

export default function SubscriptionDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [subscriptionHistory, setSubscriptionHistory] = useState<SubscriptionHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/dashboard/subscription')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.id) {
      fetchSubscriptionData()
    }
  }, [session])

  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true)
      
      // Fetch current subscription
      const subscriptionResponse = await fetch('/api/subscriptions/status')
      const subscriptionData = await subscriptionResponse.json()
      
      if (subscriptionData.success) {
        setSubscription(subscriptionData.subscription)
      }

      // Fetch subscription history (using membership history for now)
      const historyResponse = await fetch('/api/memberships/history')
      const historyData = await historyResponse.json()
      
      if (historyData.success) {
        setSubscriptionHistory(historyData.history)
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error)
      toast.error('Failed to load subscription data')
    } finally {
      setIsLoading(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading subscription details...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Subscription Dashboard</h1>
          <p className="text-gray-300">Manage your subscription and view download limits</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Current Subscription */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-900/50 border-gray-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <CrownIcon className="w-6 h-6 mr-2 text-yellow-400" />
                  Current Subscription
                </CardTitle>
              </CardHeader>
              <CardContent>
                {subscription ? (
                  <div className="space-y-6">
                    {/* Subscription Status */}
                    <div className={`bg-gradient-to-r ${subscriptionColors[subscription.planType]} p-6 rounded-xl text-white`}>
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-2xl font-bold">{subscription.planType} SUBSCRIPTION</h3>
                          <Badge className={`mt-2 ${subscriptionBadgeColors[subscription.planType]}`}>
                            {subscription.status}
                          </Badge>
                        </div>
                        <CrownIcon className="w-12 h-12 opacity-20" />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="opacity-80">Started</p>
                          <p className="font-semibold">{new Date(subscription.startDate).toLocaleDateString()}</p>
                        </div>
                        {subscription.nextBillingDate && (
                          <div>
                            <p className="opacity-80">Next Billing</p>
                            <p className="font-semibold">{new Date(subscription.nextBillingDate).toLocaleDateString()}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex flex-wrap gap-4">
                      <Link href="/downloads">
                        <Button className="bg-green-600 hover:bg-green-700">
                          <DownloadIcon className="w-4 h-4 mr-2" />
                          View Downloads
                        </Button>
                      </Link>
                      <Link href="/subscription">
                        <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
                          Manage Subscription
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <ClockIcon className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">No Active Subscription</h3>
                    <p className="text-gray-400 mb-6">Subscribe to access premium features and downloads</p>
                    <Link href="/subscription">
                      <Button className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-semibold hover:from-yellow-500 hover:to-orange-600">
                        <CrownIcon className="w-4 h-4 mr-2" />
                        Subscribe Now
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Subscription History */}
          <div>
            <Card className="bg-gray-900/50 border-gray-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <CalendarIcon className="w-5 h-5 mr-2" />
                  Subscription History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {subscriptionHistory.length > 0 ? (
                  <div className="space-y-4">
                    {subscriptionHistory.slice(0, 5).map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">{item.planType}</p>
                          <p className="text-sm text-gray-400">
                            {new Date(item.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge 
                          variant={item.status === 'ACTIVE' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {item.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400 text-center py-4">No subscription history</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
