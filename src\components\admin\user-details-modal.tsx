'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon, UserCircleIcon, ShoppingBagIcon, CalendarIcon, EnvelopeIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { User } from '@prisma/client'
import { formatCurrency } from '@/lib/utils'

interface UserOrder {
  id: string
  orderNumber: string
  totalAmount: number
  status: string
  paymentStatus: string
  createdAt: string
  items: {
    product: {
      name: string
    }
  }[]
}

interface UserDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  user: User | null
}

export function UserDetailsModal({
  isOpen,
  onClose,
  user
}: UserDetailsModalProps) {
  const [loading, setLoading] = useState(false)
  const [orders, setOrders] = useState<UserOrder[]>([])
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    completedOrders: 0
  })

  useEffect(() => {
    if (user && isOpen) {
      fetchUserDetails()
    }
  }, [user, isOpen])

  const fetchUserDetails = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/users/${user.id}/details`)
      if (response.ok) {
        const data = await response.json()
        setOrders(data.data.orders || [])
        setStats(data.data.stats || { totalOrders: 0, totalSpent: 0, completedOrders: 0 })
      }
    } catch (error) {
      console.error('Error fetching user details:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setOrders([])
    setStats({ totalOrders: 0, totalSpent: 0, completedOrders: 0 })
    onClose()
  }

  if (!isOpen || !user) return null

  const modalContent = (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="relative transform overflow-hidden rounded-lg bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
          <div className="bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium leading-6 text-white">
                User Details
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-gray-400 hover:text-white"
              >
                <XMarkIcon className="h-5 w-5" />
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Info */}
              <div className="lg:col-span-1">
                <div className="bg-gray-700/50 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <UserCircleIcon className="h-16 w-16 text-gray-400" />
                    <div className="ml-4">
                      <h4 className="text-lg font-semibold text-white">
                        {user.firstName} {user.lastName}
                      </h4>
                      <p className="text-gray-400">{user.email}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-300">
                        Joined {new Date(user.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-sm">
                      <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        user.emailVerified 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {user.emailVerified ? 'Verified' : 'Pending'}
                      </span>
                    </div>

                    <div className="flex items-center text-sm">
                      <span className="text-gray-400 mr-2">Role:</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        user.role === 'ADMIN' 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {user.role}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="mt-6 grid grid-cols-1 gap-4">
                  <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-white">{stats.totalOrders}</div>
                    <div className="text-sm text-gray-400">Total Orders</div>
                  </div>
                  <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">{formatCurrency(stats.totalSpent)}</div>
                    <div className="text-sm text-gray-400">Total Spent</div>
                  </div>
                  <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-400">{stats.completedOrders}</div>
                    <div className="text-sm text-gray-400">Completed Orders</div>
                  </div>
                </div>
              </div>

              {/* Orders */}
              <div className="lg:col-span-2">
                <div className="bg-gray-700/50 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <ShoppingBagIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <h4 className="text-lg font-semibold text-white">Recent Orders</h4>
                  </div>

                  {loading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
                    </div>
                  ) : orders.length > 0 ? (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {orders.map((order) => (
                        <div key={order.id} className="bg-gray-600/50 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium text-white">#{order.orderNumber}</div>
                            <div className="text-sm text-gray-300">
                              {new Date(order.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-lg font-semibold text-green-400">
                              {formatCurrency(order.totalAmount)}
                            </div>
                            <div className="flex space-x-2">
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                order.status === 'COMPLETED' 
                                  ? 'bg-green-100 text-green-800'
                                  : order.status === 'PENDING'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {order.status}
                              </span>
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                order.paymentStatus === 'COMPLETED' 
                                  ? 'bg-green-100 text-green-800'
                                  : order.paymentStatus === 'PENDING'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {order.paymentStatus}
                              </span>
                            </div>
                          </div>

                          <div className="text-sm text-gray-400">
                            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                            {order.items.length > 0 && (
                              <span className="ml-2">
                                {order.items.map(item => item.product.name).join(', ')}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-400">No orders found</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <Button
              type="button"
              onClick={handleClose}
              className="w-full justify-center sm:ml-3 sm:w-auto"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  )

  // Use portal to render modal at document root to avoid z-index issues
  return typeof document !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null
}
