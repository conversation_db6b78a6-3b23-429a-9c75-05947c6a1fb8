'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { 
  CheckCircleIcon, 
  DownloadIcon,
  CreditCardIcon,
  CalendarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

export default function SubscriptionSuccessPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const subscriptionId = searchParams.get('subscription_id')
  
  const [subscriptionData, setSubscriptionData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription/success')
      return
    }

    if (status === 'authenticated') {
      fetchSubscriptionData()
    }
  }, [status, router])

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscriptions/status')
      const data = await response.json()
      
      if (data.success && data.hasSubscription) {
        setSubscriptionData(data)
      } else {
        toast.error('Subscription not found')
        router.push('/subscription')
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error)
      toast.error('Failed to load subscription data')
      router.push('/subscription')
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (!subscriptionData?.hasSubscription) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Subscription Not Found</h2>
          <p className="text-gray-400 mb-8">We couldn't find your subscription details.</p>
          <Button onClick={() => router.push('/subscription')} variant="premium">
            View Subscription Plans
          </Button>
        </div>
      </div>
    )
  }

  const subscription = subscriptionData.subscription
  const downloadLimits = subscriptionData.downloadLimits

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="w-24 h-24 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircleIcon className="w-12 h-12 text-green-400" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">
            Subscription <span className="text-green-400">Activated!</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Welcome to {subscription.plan.name}! Your subscription is now active and you have full access to our premium forex tools.
          </p>
        </div>

        {/* Subscription Details Card */}
        <div className="bg-gradient-to-r from-green-900/20 to-blue-900/20 border border-green-500/30 rounded-2xl p-8 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h2 className="text-2xl font-bold text-white mb-4">Subscription Details</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckCircleIcon className="w-5 h-5 text-green-400 mr-3" />
                  <div>
                    <span className="text-gray-400">Plan:</span>
                    <span className="text-white font-semibold ml-2">{subscription.plan.name}</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <CreditCardIcon className="w-5 h-5 text-blue-400 mr-3" />
                  <div>
                    <span className="text-gray-400">Price:</span>
                    <span className="text-white font-semibold ml-2">${subscription.plan.price}/month</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="w-5 h-5 text-purple-400 mr-3" />
                  <div>
                    <span className="text-gray-400">Next Billing:</span>
                    <span className="text-white font-semibold ml-2">
                      {subscription.nextBillingDate 
                        ? new Date(subscription.nextBillingDate).toLocaleDateString()
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  <DownloadIcon className="w-5 h-5 text-yellow-400 mr-3" />
                  <div>
                    <span className="text-gray-400">Daily Downloads:</span>
                    <span className="text-white font-semibold ml-2">{subscription.plan.dailyDownloads} per day</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-bold text-white mb-4">Your Benefits</h3>
              <ul className="space-y-3">
                {subscription.plan.features.slice(0, 4).map((feature: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleIcon className="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Usage Status */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-white mb-6">Today's Usage</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <div className="text-3xl font-bold text-green-400 mb-2">
                {downloadLimits.remainingDownloads}
              </div>
              <div className="text-gray-300">Downloads Available</div>
            </div>
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <div className="text-3xl font-bold text-yellow-400 mb-2">
                {downloadLimits.dailyLimit}
              </div>
              <div className="text-gray-300">Daily Limit</div>
            </div>
            <div className="text-center p-4 bg-gray-800/30 rounded-lg">
              <div className="text-3xl font-bold text-blue-400 mb-2">
                {downloadLimits.dailyLimit - downloadLimits.remainingDownloads}
              </div>
              <div className="text-gray-300">Used Today</div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-white mb-6">What's Next?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-4 mt-1">
                <span className="text-yellow-400 font-bold">1</span>
              </div>
              <div>
                <h4 className="text-lg font-semibold text-white mb-2">Browse Our Products</h4>
                <p className="text-gray-300 text-sm mb-3">
                  Explore our extensive collection of forex EAs, indicators, and trading tools.
                </p>
                <Link href="/products">
                  <Button variant="outline" size="sm" className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black">
                    Browse Products <ArrowRightIcon className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center mr-4 mt-1">
                <span className="text-yellow-400 font-bold">2</span>
              </div>
              <div>
                <h4 className="text-lg font-semibold text-white mb-2">Join Our Community</h4>
                <p className="text-gray-300 text-sm mb-3">
                  Connect with other traders in our exclusive VIP Telegram group.
                </p>
                <a href="https://t.me/forexbotzone" target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="sm" className="border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white">
                    Join Telegram <ArrowRightIcon className="w-4 h-4 ml-2" />
                  </Button>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => router.push('/products')}
            variant="premium"
            size="lg"
          >
            Start Downloading
          </Button>
          
          <Button
            onClick={() => router.push('/subscription/manage')}
            variant="outline"
            size="lg"
            className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black"
          >
            Manage Subscription
          </Button>
        </div>

        {/* Important Notice */}
        <div className="mt-12 text-center bg-blue-500/10 border border-blue-500/20 rounded-2xl p-6">
          <h4 className="text-lg font-semibold text-white mb-2">Important Notice</h4>
          <p className="text-gray-300 text-sm">
            Your subscription will automatically renew monthly. You can cancel anytime from your subscription management page.
            You'll receive email confirmations for all billing activities.
          </p>
        </div>
      </div>
    </div>
  )
}
