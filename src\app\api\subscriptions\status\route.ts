import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserSubscription, checkSubscriptionDownloadLimit } from '@/lib/subscription-manager'
import { securityHeaders } from '@/lib/security'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { 
        status: 401,
        headers: securityHeaders
      })
    }

    console.log('[Subscription Status] Getting status for user:', session.user.id)

    // Get user's subscription
    const subscription = await getUserSubscription(session.user.id)
    
    if (!subscription) {
      return NextResponse.json({
        success: true,
        hasSubscription: false,
        subscription: null,
        downloadLimits: null
      }, {
        headers: securityHeaders
      })
    }

    // Get download limits
    const downloadLimits = await checkSubscriptionDownloadLimit(session.user.id)

    console.log('[Subscription Status] Subscription found:', {
      id: subscription.id,
      planType: subscription.planType,
      status: subscription.status,
      isActive: subscription.isActive,
      remainingDownloads: downloadLimits.remainingDownloads
    })

    return NextResponse.json({
      success: true,
      hasSubscription: true,
      subscription: {
        id: subscription.id,
        planType: subscription.planType,
        status: subscription.status,
        isActive: subscription.isActive,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        nextBillingDate: subscription.nextBillingDate,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        cancelledAt: subscription.cancelledAt,
        plan: {
          name: subscription.plan.name,
          price: subscription.plan.price,
          dailyDownloads: subscription.plan.dailyDownloads,
          features: subscription.plan.features
        },
        paypalSubscriptionId: subscription.paypalSubscriptionId
      },
      downloadLimits: {
        canDownload: downloadLimits.canDownload,
        remainingDownloads: downloadLimits.remainingDownloads,
        dailyLimit: downloadLimits.dailyLimit,
        planType: downloadLimits.planType,
        resetTime: downloadLimits.resetTime
      }
    }, {
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('[Subscription Status] Error getting subscription status:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Failed to get subscription status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { 
      status: 500,
      headers: securityHeaders
    })
  }
}
