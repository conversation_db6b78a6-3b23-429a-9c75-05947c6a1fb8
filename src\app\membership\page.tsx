'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function MembershipRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new subscription page
    router.replace('/subscription')
  }, [router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
        <p className="text-gray-300">Redirecting to subscription page...</p>
      </div>
    </div>
  )
}
