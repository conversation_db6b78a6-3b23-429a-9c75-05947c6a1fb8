import { ProductWithRelations } from '@/types'

interface ProductStructuredDataProps {
  product: ProductWithRelations
  baseUrl: string
}

export function ProductStructuredData({ product, baseUrl }: ProductStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.shortDescription || product.description,
    "image": product.images?.map(img => `${baseUrl}${img}`) || [],
    "url": `${baseUrl}/products/${product.slug}`,
    "sku": product.id,
    "brand": {
      "@type": "Brand",
      "name": "Forex Bot Zone"
    },
    "category": product.category?.name,
    "offers": {
      "@type": "Offer",
      "price": product.isOnSale && product.salePrice ? product.salePrice.toString() : product.price.toString(),
      "priceCurrency": "USD",
      "availability": product.status === 'PUBLISHED' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
      "url": `${baseUrl}/products/${product.slug}`,
      "seller": {
        "@type": "Organization",
        "name": "Forex Bot Zone",
        "url": baseUrl
      }
    },
    "aggregateRating": product.averageRating && product._count?.reviews ? {
      "@type": "AggregateRating",
      "ratingValue": product.averageRating,
      "reviewCount": product._count.reviews,
      "bestRating": 5,
      "worstRating": 1
    } : undefined
  }

  // Remove undefined values
  const cleanStructuredData = JSON.parse(JSON.stringify(structuredData))

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(cleanStructuredData) }}
    />
  )
}

interface OrganizationStructuredDataProps {
  baseUrl: string
}

export function OrganizationStructuredData({ baseUrl }: OrganizationStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Forex Bot Zone",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": "Premium Forex Expert Advisors and Indicators at Special Prices - Professional trading bots for serious traders",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": `${baseUrl}/contact`,
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://t.me/forexbotzone"
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

interface WebsiteStructuredDataProps {
  baseUrl: string
}

export function WebsiteStructuredData({ baseUrl }: WebsiteStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Forex Bot Zone",
    "url": baseUrl,
    "description": "Premium Forex Expert Advisors and Indicators at Special Prices",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/products?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string
    url: string
  }>
  baseUrl: string
}

export function BreadcrumbStructuredData({ items, baseUrl }: BreadcrumbStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `${baseUrl}${item.url}`
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
