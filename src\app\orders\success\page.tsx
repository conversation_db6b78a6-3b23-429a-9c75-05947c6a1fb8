import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { CheckCircleIcon, ClockIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import Image from 'next/image'
import { formatCurrency } from '@/lib/utils'
import { TelegramPopup } from '@/components/ui/telegram-popup'

interface OrderSuccessPageProps {
  searchParams: {
    orderId?: string
    status?: string
  }
}

async function getOrder(orderId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                images: true,
                shortDescription: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    return order
  } catch (error) {
    console.error('Error fetching order:', error)
    return null
  }
}

async function OrderSuccessContent({ orderId, status }: { orderId: string, status?: string }) {
  const order = await getOrder(orderId)

  if (!order) {
    notFound()
  }

  const isStripePayment = order.paymentMethod === 'stripe'
  const isPayPalPayment = order.paymentMethod === 'paypal'
  const isManualPayment = order.paymentMethod === 'manual'
  const isPaid = order.paymentStatus === 'PAID' || order.paymentStatus === 'COMPLETED'
  const isPending = order.paymentStatus === 'PENDING' || status === 'pending'
  const isProcessing = order.status === 'PROCESSING'

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white/5 backdrop-blur-md border border-white/10 shadow-lg rounded-lg overflow-hidden">
          {/* Header */}
          <div className={`${isPaid ? 'bg-green-500/10 border-green-500/20' : isPending ? 'bg-yellow-500/10 border-yellow-500/20' : 'bg-blue-500/10 border-blue-500/20'} border-b px-6 py-8 text-center`}>
            {isPaid ? (
              <CheckCircleIcon className="mx-auto h-16 w-16 text-green-400 mb-4" />
            ) : (
              <ClockIcon className="mx-auto h-16 w-16 text-yellow-400 mb-4" />
            )}
            <h1 className="text-3xl font-bold text-white mb-2">
              {isPaid ? 'Order Completed!' : isPending ? 'Payment Processing' : 'Order Received!'}
            </h1>
            <p className="text-lg text-gray-300">
              {isPaid
                ? 'Your payment has been processed successfully.'
                : isPending
                ? 'Your payment is being processed. You will receive confirmation once completed.'
                : 'Thank you for your order. We\'ll process it shortly.'
              }
            </p>
          </div>

          {/* Order Details */}
          <div className="px-6 py-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Order Information</h3>
                <div className="space-y-2 text-gray-300">
                  <p><span className="font-medium text-white">Order ID:</span> {order.orderNumber}</p>
                  <p><span className="font-medium text-white">Date:</span> {new Date(order.createdAt).toLocaleDateString()}</p>
                  <p><span className="font-medium text-white">Total:</span> {formatCurrency(Number(order.totalAmount))}</p>
                  <p><span className="font-medium text-white">Payment Method:</span> {isStripePayment ? 'Credit Card' : isPayPalPayment ? 'PayPal' : 'Manual Payment'}</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Customer Information</h3>
                <div className="space-y-2 text-gray-300">
                  <p><span className="font-medium text-white">Name:</span> {order.firstName} {order.lastName}</p>
                  <p><span className="font-medium text-white">Email:</span> {order.email}</p>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            <div className="mb-8">
              {isPaid && (isStripePayment || isPayPalPayment) && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <div className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
                    <p className="text-green-300 font-medium">
                      Payment confirmed! Your downloads are ready.
                    </p>
                  </div>
                </div>
              )}

              {isPending && isPayPalPayment && (
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 text-yellow-400 mr-2" />
                    <p className="text-yellow-300 font-medium">
                      PayPal payment is being processed. You will receive an email confirmation once the payment is completed.
                    </p>
                  </div>
                </div>
              )}

              {isPending && isManualPayment && (
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 text-yellow-400 mr-2" />
                    <div>
                      <p className="text-yellow-300 font-medium">
                        Payment verification in progress
                      </p>
                      <p className="text-yellow-200 text-sm mt-1">
                        After payment is verified, your purchase will be completed and download links will be available.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Order Items */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-white mb-4">Order Items</h3>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-start gap-4 p-4 border border-white/10 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-700">
                        {item.product.images && item.product.images.length > 0 ? (
                          <Image
                            src={item.product.images[0]}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <DocumentArrowDownIcon className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-white mb-1 truncate">{item.product.name}</h4>
                      {item.product.shortDescription && (
                        <p className="text-sm text-gray-400 mb-2 line-clamp-2">{item.product.shortDescription}</p>
                      )}
                      <div className="flex items-center gap-4 text-sm text-gray-300">
                        <span>Quantity: {item.quantity}</span>
                        <span>•</span>
                        <span>Price: {formatCurrency(Number(item.price))}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col items-end gap-2">
                      <p className="font-semibold text-white text-lg">{formatCurrency(Number(item.price) * item.quantity)}</p>
                      {isPaid && isStripePayment && (
                        <Link
                          href={`/downloads?product=${item.product.id}`}
                          className="inline-flex items-center mt-2 px-3 py-1 bg-yellow-500 hover:bg-yellow-600 text-black text-sm rounded-md transition-colors font-medium"
                        >
                          <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                          Download
                        </Link>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isPaid && (
                <Link
                  href="/downloads"
                  className="inline-flex items-center justify-center px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-md transition-colors"
                >
                  <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                  View All Downloads
                </Link>
              )}

              <Link
                href="/products"
                className="inline-flex items-center justify-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-md border border-white/20 transition-colors"
              >
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Telegram Popup */}
      <TelegramPopup show={true} delay={4000} />
    </div>
  )
}

export default function OrderSuccessPage({ searchParams }: OrderSuccessPageProps) {
  const orderId = searchParams.orderId
  const status = searchParams.status

  if (!orderId) {
    notFound()
  }

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading order details...</p>
        </div>
      </div>
    }>
      <OrderSuccessContent orderId={orderId} status={status} />
    </Suspense>
  )
}
