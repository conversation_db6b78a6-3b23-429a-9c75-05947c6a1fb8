import { NextRequest, NextResponse } from 'next/server'
import { verifyPayPalWebhook } from '@/lib/paypal'
import { prisma } from '@/lib/prisma'
import { securityHeaders } from '@/lib/security'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headers = Object.fromEntries(request.headers.entries())

    // Verify webhook signature
    const webhookId = process.env.PAYPAL_WEBHOOK_ID
    if (!webhookId) {
      console.error('PayPal webhook ID not configured')
      return NextResponse.json({ received: true }, { 
        status: 200,
        headers: securityHeaders
      })
    }

    const isValid = await verifyPayPalWebhook(headers, body, webhookId)
    if (!isValid) {
      console.error('Invalid PayPal webhook signature')
      return NextResponse.json({ error: 'Invalid signature' }, { 
        status: 400,
        headers: securityHeaders
      })
    }

    const event = JSON.parse(body)
    console.log('PayPal webhook event:', event.event_type)

    // Handle different webhook events
    switch (event.event_type) {
      case 'CHECKOUT.ORDER.APPROVED':
        await handleOrderApproved(event)
        break
      
      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCaptured(event)
        break
      
      case 'PAYMENT.CAPTURE.DENIED':
      case 'PAYMENT.CAPTURE.DECLINED':
        await handlePaymentFailed(event)
        break
      
      default:
        console.log(`Unhandled PayPal webhook event: ${event.event_type}`)
    }

    return NextResponse.json({ received: true }, {
      headers: securityHeaders
    })

  } catch (error) {
    console.error('PayPal webhook error:', error)
    return NextResponse.json({ error: 'Webhook processing failed' }, { 
      status: 500,
      headers: securityHeaders
    })
  }
}

async function handleOrderApproved(event: any) {
  try {
    const paypalOrderId = event.resource.id
    
    // Update order status to approved
    await prisma.order.updateMany({
      where: { paypalOrderId },
      data: { 
        status: 'PROCESSING',
        metadata: {
          paypalWebhookEvent: event
        }
      }
    })

    console.log(`PayPal order approved: ${paypalOrderId}`)
  } catch (error) {
    console.error('Error handling order approved:', error)
  }
}

async function handlePaymentCaptured(event: any) {
  try {
    const captureId = event.resource.id
    const paypalOrderId = event.resource.supplementary_data?.related_ids?.order_id

    if (paypalOrderId) {
      // Update order status to completed
      await prisma.order.updateMany({
        where: { paypalOrderId },
        data: { 
          status: 'COMPLETED',
          paymentStatus: 'COMPLETED',
          paymentIntentId: captureId,
          metadata: {
            paypalWebhookEvent: event
          }
        }
      })

      console.log(`PayPal payment captured: ${captureId} for order: ${paypalOrderId}`)
    }
  } catch (error) {
    console.error('Error handling payment captured:', error)
  }
}

async function handlePaymentFailed(event: any) {
  try {
    const paypalOrderId = event.resource.supplementary_data?.related_ids?.order_id

    if (paypalOrderId) {
      // Update order status to failed
      await prisma.order.updateMany({
        where: { paypalOrderId },
        data: { 
          status: 'CANCELLED',
          paymentStatus: 'FAILED',
          metadata: {
            paypalWebhookEvent: event
          }
        }
      })

      console.log(`PayPal payment failed for order: ${paypalOrderId}`)
    }
  } catch (error) {
    console.error('Error handling payment failed:', error)
  }
}
