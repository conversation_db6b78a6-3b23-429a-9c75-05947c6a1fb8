import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'
import { EmailTemplateType } from '@prisma/client'
import { sanitizeHtml, sanitizeString, securityHeaders, createSafeErrorResponse } from '@/lib/security'

const createEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  subject: z.string().min(1, 'Subject is required'),
  htmlContent: z.string().min(1, 'HTML content is required'),
  textContent: z.string().optional(),
  type: z.nativeEnum(EmailTemplateType),
  variables: z.record(z.string()).optional(),
  isActive: z.boolean().default(true),
})

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')

    const where = type ? { type: type as EmailTemplateType } : {}

    const templates = await prisma.emailTemplate.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: { emailLogs: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: templates
    })

  } catch (error: any) {
    console.error('Error fetching email templates:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch email templates'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const body = await request.json()
    const validatedData = createEmailTemplateSchema.parse(body)

    // Sanitize HTML content to prevent XSS
    const sanitizedData = {
      ...validatedData,
      name: sanitizeString(validatedData.name),
      subject: sanitizeString(validatedData.subject),
      htmlContent: sanitizeHtml(validatedData.htmlContent),
      textContent: validatedData.textContent ? sanitizeString(validatedData.textContent) : undefined
    }

    const template = await prisma.emailTemplate.create({
      data: sanitizedData
    })

    return NextResponse.json({
      success: true,
      message: 'Email template created successfully',
      data: template
    }, {
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('Error creating email template:', error)

    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
