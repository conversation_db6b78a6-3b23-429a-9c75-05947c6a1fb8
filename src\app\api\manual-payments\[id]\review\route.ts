import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'
import { sendOrderConfirmationEmail } from '@/lib/email'
import { z } from 'zod'

const reviewPaymentSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  adminNotes: z.string().optional()
})

// PUT /api/manual-payments/[id]/review - Review manual payment (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const admin = await requireAdmin()
    const body = await request.json()
    const { status, adminNotes } = reviewPaymentSchema.parse(body)

    // Get the manual payment with order details
    const manualPayment = await prisma.manualPayment.findUnique({
      where: { id: params.id },
      include: {
        order: {
          include: {
            user: true,
            items: {
              include: {
                product: {
                  include: {
                    downloadFiles: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!manualPayment) {
      return NextResponse.json({
        success: false,
        message: 'Manual payment not found'
      }, { status: 404 })
    }

    if (manualPayment.status !== 'PENDING') {
      return NextResponse.json({
        success: false,
        message: 'Manual payment has already been reviewed'
      }, { status: 400 })
    }

    // Use database transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // Update manual payment status
      const updatedPayment = await tx.manualPayment.update({
        where: { id: params.id },
        data: {
          status,
          reviewedAt: new Date(),
          reviewedBy: admin.id,
          adminNotes
        }
      })

      if (status === 'APPROVED') {
        console.log(`Approving manual payment ${params.id} for order ${manualPayment.orderId}`)

        // Update order status to completed
        await tx.order.update({
          where: { id: manualPayment.orderId },
          data: {
            status: 'COMPLETED',
            paymentStatus: 'COMPLETED',
            adminNotes
          }
        })

        // Create purchase records for each product (use upsert to handle duplicates)
        const purchasePromises = manualPayment.order.items.map(item => {
          console.log(`Creating/updating purchase record for user ${manualPayment.order.userId}, product ${item.productId}`)

          return tx.purchase.upsert({
            where: {
              userId_productId: {
                userId: manualPayment.order.userId!,
                productId: item.productId
              }
            },
            update: {
              // Update existing purchase with latest order info
              orderId: manualPayment.orderId,
              price: item.price,
              status: 'COMPLETED',
              updatedAt: new Date()
            },
            create: {
              userId: manualPayment.order.userId!,
              productId: item.productId,
              orderId: manualPayment.orderId,
              price: item.price,
              status: 'COMPLETED'
            }
          })
        })

        // Wait for all purchase records to be created
        await Promise.all(purchasePromises)

        // Clear user's cart
        if (manualPayment.order.userId) {
          try {
            await tx.cartItem.deleteMany({
              where: { userId: manualPayment.order.userId }
            })
            console.log(`Cleared cart for user ${manualPayment.order.userId}`)
          } catch (cartError) {
            console.error('Failed to clear user cart:', cartError)
            // Don't fail the transaction if cart clearing fails
          }
        }

      } else if (status === 'REJECTED') {
        console.log(`Rejecting manual payment ${params.id} for order ${manualPayment.orderId}`)

        // Update order status to cancelled
        await tx.order.update({
          where: { id: manualPayment.orderId },
          data: {
            status: 'CANCELLED',
            paymentStatus: 'FAILED',
            adminNotes
          }
        })
      }

      return updatedPayment
    }, {
      maxWait: 10000, // 10 seconds
      timeout: 30000, // 30 seconds
    })

    // Send order confirmation email after successful transaction (outside transaction)
    if (status === 'APPROVED') {
      try {
        await sendOrderConfirmationEmail(manualPayment.order.email, {
          orderNumber: manualPayment.order.orderNumber,
          customerName: `${manualPayment.order.firstName} ${manualPayment.order.lastName}`,
          items: manualPayment.order.items.map(item => ({
            name: item.product.name,
            quantity: item.quantity,
            price: Number(item.price)
          })),
          total: Number(manualPayment.order.totalAmount),
          downloadUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/downloads`
        })
        console.log(`Order confirmation email sent for order ${manualPayment.order.orderNumber}`)
      } catch (emailError) {
        console.error('Failed to send order confirmation email:', emailError)
        // Don't fail the response if email fails - the payment is already approved
      }
    }

    return NextResponse.json({
      success: true,
      message: `Manual payment ${status.toLowerCase()} successfully`,
      data: {
        ...result,
        amount: Number(result.amount)
      }
    })

  } catch (error) {
    console.error('Error reviewing manual payment:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to review manual payment'
    }, { status: 500 })
  }
}
