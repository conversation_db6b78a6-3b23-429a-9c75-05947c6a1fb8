import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPayPalOrder } from '@/lib/paypal'
import { prisma } from '@/lib/prisma'
import { generateOrderNumber } from '@/lib/utils'
import { z } from 'zod'
import { securityHeaders, rateLimit, createSafeErrorResponse } from '@/lib/security'

const createPayPalOrderSchema = z.object({
  amount: z.number().positive().optional(),
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1)
  })).min(1, 'At least one item is required'),
  customerInfo: z.object({
    email: z.string().email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1)
  }),
  couponCode: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10 // 10 PayPal orders per 15 minutes per IP
    })(request)

    if (!rateLimitResult.success) {
      return NextResponse.json({
        success: false,
        message: rateLimitResult.error
      }, { 
        status: 429,
        headers: securityHeaders
      })
    }

    // Get session (required for authenticated checkout only)
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, {
        status: 401,
        headers: securityHeaders
      })
    }

    const body = await request.json()
    const { items, customerInfo, couponCode } = createPayPalOrderSchema.parse(body)

    // Handle regular product purchase
    const productIds = items.map(item => item.productId)
    const products = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        status: 'PUBLISHED'
      }
    })

    if (products.length !== productIds.length) {
      return NextResponse.json({
        success: false,
        message: 'Some products are not available'
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // Calculate amounts for products
    let subtotalAmount = 0
    const orderItems = items.map(item => {
      const product = products.find(p => p.id === item.productId)!
      const price = product.isOnSale && product.salePrice
        ? Number(product.salePrice)
        : Number(product.price)

      subtotalAmount += price * item.quantity

      return {
        productId: product.id,
        quantity: item.quantity,
        price: price
      }
    })



    // Apply coupon if provided
    let coupon = null
    let discountAmount = 0
    let totalDiscountAmount = 0

    if (couponCode) {
      coupon = await prisma.coupon.findFirst({
        where: {
          code: couponCode,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        }
      })

      if (coupon) {
        if (coupon.type === 'PERCENTAGE') {
          discountAmount = (subtotalAmount * Number(coupon.value)) / 100
        } else {
          discountAmount = Number(coupon.value)
        }
        
        // Ensure discount doesn't exceed subtotal
        discountAmount = Math.min(discountAmount, subtotalAmount)
        totalDiscountAmount = discountAmount
      }
    }

    const finalAmount = subtotalAmount - totalDiscountAmount

    if (finalAmount <= 0) {
      return NextResponse.json({
        success: false,
        message: 'Invalid order amount'
      }, { 
        status: 400,
        headers: securityHeaders
      })
    }

    // Create order in database
    const orderNumber = generateOrderNumber()

    let orderData: any = {
      orderNumber,
      userId: session.user.id,
      status: 'PENDING',
      paymentStatus: 'PENDING',
      paymentMethod: 'PAYPAL',
      subtotalAmount,
      discountAmount: totalDiscountAmount,
      totalAmount: finalAmount,
      firstName: customerInfo.firstName,
      lastName: customerInfo.lastName,
      email: customerInfo.email,
      couponId: coupon?.id,
    }

    // For product purchases, create order items
    orderData.items = {
      create: orderItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price
      }))
    }

    const order = await prisma.order.create({
      data: orderData,
      include: {
        items: {
          include: {
            product: true
          }
        }
      }
    })

    // Prepare PayPal items
    const paypalItems = order.items.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      unit_amount: {
        currency_code: 'USD',
        value: item.price.toFixed(2)
      }
    }))

    // Create PayPal order
    const paypalOrder = await createPayPalOrder(
      finalAmount,
      'USD',
      paypalItems,
      order.id
    )

    // Update order with PayPal order ID
    await prisma.order.update({
      where: { id: order.id },
      data: {
        paypalOrderId: paypalOrder.id,
        metadata: {
          ...(order.metadata as any || {}),
          paypalOrderData: paypalOrder
        }
      }
    })

    console.log('[PayPal API] Order created successfully:', {
      paypalOrderId: paypalOrder.id,
      orderId: order.id,
      orderNumber: order.orderNumber,
      total: finalAmount
    })

    return NextResponse.json({
      success: true,
      orderId: paypalOrder.id, // PayPal order ID for frontend
      orderID: paypalOrder.id, // Alternative field name for compatibility
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        subtotal: subtotalAmount,
        discount: totalDiscountAmount,
        total: finalAmount,
        coupon: coupon ? {
          code: coupon.code,
          name: coupon.name,
          type: coupon.type,
          value: coupon.value
        } : null
      }
    }, {
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('[PayPal API] Error creating order:', {
      error: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      requestBody: body
    })

    if (error.name === 'ZodError') {
      console.error('[PayPal API] Validation error:', error.errors)
      return NextResponse.json({
        success: false,
        message: 'Invalid request data',
        errors: error.errors
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // PayPal specific errors
    if (error.message?.includes('PAYPAL')) {
      console.error('[PayPal API] PayPal service error:', error)
      return NextResponse.json({
        success: false,
        message: 'PayPal service temporarily unavailable. Please try again.'
      }, {
        status: 503,
        headers: securityHeaders
      })
    }

    const safeError = createSafeErrorResponse(error, process.env.NODE_ENV === 'development')
    return NextResponse.json(safeError, {
      status: 500,
      headers: securityHeaders
    })
  }
}
