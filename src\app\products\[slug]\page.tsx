import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { ProductDetails } from '@/components/products/product-details'
import { RelatedProducts } from '@/components/products/related-products'
import { ProductStructuredData, BreadcrumbStructuredData } from '@/components/seo/structured-data'

interface ProductPageProps {
  params: {
    slug: string
  }
}

async function getProduct(slug: string) {
  const product = await prisma.product.findUnique({
    where: { 
      slug,
      status: 'PUBLISHED'
    },
    include: {
      category: true,
      reviews: {
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }
    }
  })

  return product
}

async function getRelatedProducts(categoryId: string, currentProductId: string) {
  const products = await prisma.product.findMany({
    where: {
      categoryId,
      status: 'PUBLISHED',
      id: {
        not: currentProductId
      }
    },
    include: {
      category: true
    },
    take: 4,
    orderBy: {
      createdAt: 'desc'
    }
  })

  return products
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const product = await getProduct(params.slug)
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  if (!product) {
    return {
      title: 'Product Not Found - Forex Bot Zone',
      description: 'The requested product could not be found.',
    }
  }

  const price = product.isOnSale && product.salePrice ? Number(product.salePrice) : Number(product.price)
  const originalPrice = product.originalPrice ? Number(product.originalPrice) : null

  return {
    title: product.metaTitle || `${product.name} - Premium Forex ${product.category.name} | Forex Bot Zone`,
    description: product.metaDescription || `${product.shortDescription} Get this premium ${product.category.name.toLowerCase()} for only $${price}. Professional trading tools for serious traders.`,
    keywords: [
      ...(product.tags || []),
      'forex bot',
      'expert advisor',
      'trading robot',
      product.category.name.toLowerCase(),
      'MT4',
      'MT5',
      'automated trading'
    ].join(', '),
    alternates: {
      canonical: `/products/${product.slug}`,
    },
    openGraph: {
      title: product.name,
      description: product.shortDescription || product.description,
      images: product.images?.map(img => ({
        url: img.startsWith('http') ? img : `${baseUrl}${img}`,
        width: 800,
        height: 600,
        alt: product.name,
      })) || [],
      type: 'website',
      url: `/products/${product.slug}`,
      siteName: 'Forex Bot Zone',
    },
    twitter: {
      card: 'summary_large_image',
      title: product.name,
      description: product.shortDescription || product.description,
      images: product.images?.map(img => img.startsWith('http') ? img : `${baseUrl}${img}`) || [],
    },
    other: {
      'product:price:amount': price.toString(),
      'product:price:currency': 'USD',
      ...(originalPrice && originalPrice > price ? { 'product:original_price:amount': originalPrice.toString() } : {}),
      'product:availability': product.status === 'PUBLISHED' ? 'in stock' : 'out of stock',
      'product:category': product.category.name,
    },
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const product = await getProduct(params.slug)

  if (!product) {
    notFound()
  }

  const relatedProducts = await getRelatedProducts(product.categoryId, product.id)
  const baseUrl = process.env.SITE_URL || 'http://localhost:3000'

  // Breadcrumb data
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Products', url: '/products' },
    { name: product.category.name, url: `/categories/${product.category.slug}` },
    { name: product.name, url: `/products/${product.slug}` }
  ]

  return (
    <>
      <ProductStructuredData product={product} baseUrl={baseUrl} />
      <BreadcrumbStructuredData items={breadcrumbItems} baseUrl={baseUrl} />

      <div className="min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ProductDetails product={product} />

          {relatedProducts.length > 0 && (
            <div className="mt-16">
              <RelatedProducts products={relatedProducts} />
            </div>
          )}
        </div>
      </div>
    </>
  )
}
