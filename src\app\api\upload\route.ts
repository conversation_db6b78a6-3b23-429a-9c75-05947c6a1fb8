import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth-utils'
import {
  uploadFile,
  generateFileKey,
  isValidFileType,
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZES,
  generateSignedUrl
} from '@/lib/cloudflare-r2'
import { canAccessFile, rateLimit, securityHeaders } from '@/lib/security'

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string // 'product', 'image', 'avatar'
    
    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided'
      }, { status: 400 })
    }

    if (!type || !['product', 'image', 'avatar'].includes(type)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid file type specified'
      }, { status: 400 })
    }

    // Validate file type
    let allowedTypes: string[] = []
    let maxSize: number = 0

    switch (type) {
      case 'product':
        allowedTypes = ALLOWED_FILE_TYPES.PRODUCT_FILES
        maxSize = MAX_FILE_SIZES.PRODUCT_FILE
        break
      case 'image':
        allowedTypes = ALLOWED_FILE_TYPES.IMAGES
        maxSize = MAX_FILE_SIZES.IMAGE
        break
      case 'avatar':
        allowedTypes = ALLOWED_FILE_TYPES.IMAGES
        maxSize = MAX_FILE_SIZES.IMAGE
        break
    }

    if (!isValidFileType(file.name, allowedTypes)) {
      return NextResponse.json({
        success: false,
        message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
      }, { status: 400 })
    }

    // Validate file size
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        message: `File too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`
      }, { status: 400 })
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())
    
    // Generate unique file key
    const fileKey = generateFileKey(type as any, file.name, user.id)
    
    // Upload to R2
    const uploadResult = await uploadFile(
      buffer,
      fileKey,
      file.type,
      {
        originalName: file.name,
        uploadedBy: user.id,
        uploadedAt: new Date().toISOString()
      }
    )

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        key: uploadResult.key,
        url: uploadResult.url,
        size: uploadResult.size,
        originalName: file.name,
        type: file.type
      }
    })

  } catch (error: any) {
    console.error('Error uploading file:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to upload file'
    }, { status: 500 })
  }
}

// GET /api/upload/signed-url - Generate signed URL for secure downloads
export async function GET(request: NextRequest) {
  try {
    // Rate limiting for download requests
    const rateLimitResult = await rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 50 // 50 downloads per 15 minutes per IP
    })(request)

    if (!rateLimitResult.success) {
      return NextResponse.json({
        success: false,
        message: rateLimitResult.error
      }, {
        status: 429,
        headers: securityHeaders
      })
    }

    const user = await requireAuth()
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')
    const expiresIn = parseInt(searchParams.get('expiresIn') || '3600')

    if (!key) {
      return NextResponse.json({
        success: false,
        message: 'File key is required'
      }, {
        status: 400,
        headers: securityHeaders
      })
    }

    // Authorization check - ensure user has access to this file
    const hasAccess = await canAccessFile(user.id, key)
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        message: 'Access denied. You do not have permission to download this file.'
      }, {
        status: 403,
        headers: securityHeaders
      })
    }

    const signedUrl = await generateSignedUrl(key, expiresIn)

    return NextResponse.json({
      success: true,
      data: {
        url: signedUrl,
        expiresIn
      }
    }, {
      headers: securityHeaders
    })

  } catch (error: any) {
    console.error('Error generating signed URL:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to generate download URL'
    }, { status: 500 })
  }
}
