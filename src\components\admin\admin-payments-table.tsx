'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CogIcon
} from '@heroicons/react/24/outline'
import Image from 'next/image'
import toast from 'react-hot-toast'
import { formatCurrency } from '@/lib/utils'
import { PaymentMethodsSettings } from '@/components/admin/payment-methods-settings'

interface ManualPayment {
  id: string
  orderId: string
  paymentMethod: string
  amount: number
  currency: string
  walletAddress: string
  transactionHash?: string
  proofImageUrl: string
  proofFileName: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PROCESSING'
  submittedAt: string
  reviewedAt?: string
  adminNotes?: string
  order: {
    orderNumber: string
    email: string
    firstName: string
    lastName: string
    totalAmount: number
    user?: {
      firstName: string
      lastName: string
      email: string
    }
    items: Array<{
      quantity: number
      price: number
      product: {
        name: string
        slug: string
      }
    }>
  }
  reviewer?: {
    firstName: string
    lastName: string
    email: string
  }
}

export function AdminPaymentsTable() {
  const [activeTab, setActiveTab] = useState<'payments' | 'methods'>('payments')
  const [payments, setPayments] = useState<ManualPayment[]>([])
  const [filteredPayments, setFilteredPayments] = useState<ManualPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('PENDING')
  const [selectedPayment, setSelectedPayment] = useState<ManualPayment | null>(null)
  const [reviewNotes, setReviewNotes] = useState('')
  const [isReviewing, setIsReviewing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchPayments()
  }, [selectedStatus])

  useEffect(() => {
    filterPayments()
  }, [payments, searchQuery])

  const fetchPayments = async (retryCount = 0) => {
    const maxRetries = 2

    try {
      setLoading(true)
      const response = await fetch(`/api/manual-payments?status=${selectedStatus}`, {
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(15000) // 15 seconds timeout
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setPayments(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch payments')
      }
    } catch (error) {
      console.error('Error fetching payments:', error)

      // Check if it's a network/connection error and retry
      const isNetworkError = error instanceof TypeError ||
                            (error instanceof Error && error.message.includes('fetch')) ||
                            (error instanceof Error && error.message.includes('timeout'))

      if (isNetworkError && retryCount < maxRetries) {
        console.log(`Retrying fetch payments (attempt ${retryCount + 1}/${maxRetries})`)

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))

        return fetchPayments(retryCount + 1)
      }

      // Show error message only if not retrying
      if (error instanceof Error) {
        toast.error(error.message.includes('timeout') ? 'Request timed out while fetching payments' : error.message)
      } else {
        toast.error('Failed to fetch payments')
      }
    } finally {
      setLoading(false)
    }
  }

  const filterPayments = () => {
    if (!searchQuery.trim()) {
      setFilteredPayments(payments)
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = payments.filter(payment =>
      payment.order.orderNumber.toLowerCase().includes(query) ||
      payment.order.email.toLowerCase().includes(query) ||
      payment.order.firstName.toLowerCase().includes(query) ||
      payment.order.lastName.toLowerCase().includes(query) ||
      payment.paymentMethod.toLowerCase().includes(query) ||
      payment.currency.toLowerCase().includes(query) ||
      (payment.transactionHash && payment.transactionHash.toLowerCase().includes(query))
    )
    setFilteredPayments(filtered)
  }

  const handleReviewPayment = async (paymentId: string, status: 'APPROVED' | 'REJECTED', retryCount = 0) => {
    setIsReviewing(true)
    const maxRetries = 2

    try {
      const response = await fetch(`/api/manual-payments/${paymentId}/review`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          adminNotes: reviewNotes
        }),
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(30000) // 30 seconds timeout
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        toast.success(`Payment ${status.toLowerCase()} successfully`)
        setSelectedPayment(null)
        setReviewNotes('')
        // Refresh the payments list to show updated status
        await fetchPayments()
      } else {
        throw new Error(data.message || 'Failed to review payment')
      }
    } catch (error) {
      console.error('Error reviewing payment:', error)

      // Check if it's a network/connection error and retry
      const isNetworkError = error instanceof TypeError ||
                            (error instanceof Error && error.message.includes('fetch')) ||
                            (error instanceof Error && error.message.includes('connection'))

      if (isNetworkError && retryCount < maxRetries) {
        console.log(`Retrying payment review (attempt ${retryCount + 1}/${maxRetries})`)
        toast.loading(`Retrying... (${retryCount + 1}/${maxRetries})`)

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))

        return handleReviewPayment(paymentId, status, retryCount + 1)
      }

      // Show appropriate error message
      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('AbortError')) {
          toast.error('Request timed out. Please check if the payment was processed and refresh the page.')
        } else if (error.message.includes('connection') || error.message.includes('network')) {
          toast.error('Connection error. The payment may have been processed. Please refresh the page to check.')
        } else {
          toast.error(error.message)
        }
      } else {
        toast.error('Failed to review payment. Please refresh the page to check if it was processed.')
      }

      // Refresh payments list in case the operation actually succeeded
      setTimeout(() => {
        fetchPayments()
      }, 2000)

    } finally {
      setIsReviewing(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { className: "bg-yellow-900/20 text-yellow-400 border-yellow-700", text: "Pending" },
      APPROVED: { className: "bg-green-900/20 text-green-400 border-green-700", text: "Approved" },
      REJECTED: { className: "bg-red-900/20 text-red-400 border-red-700", text: "Rejected" },
      PROCESSING: { className: "bg-blue-900/20 text-blue-400 border-blue-700", text: "Processing" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || { className: "", text: status }
    
    return (
      <Badge variant="secondary" className={config.className}>
        {config.text}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('payments')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'payments'
                ? 'border-yellow-400 text-yellow-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <DocumentIcon className="w-5 h-5 inline mr-2" />
            Manual Payments
          </button>
          <button
            onClick={() => setActiveTab('methods')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'methods'
                ? 'border-yellow-400 text-yellow-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <CogIcon className="w-5 h-5 inline mr-2" />
            Payment Methods
          </button>
        </nav>
      </div>

      {activeTab === 'payments' ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">Manual Payment Reviews</h2>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="md:hidden"
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Search and Filters */}
          <div className={`space-y-4 ${showFilters ? 'block' : 'hidden md:block'}`}>
            {/* Search Bar */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by order number, email, name, payment method, or transaction hash..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-400 font-medium mr-2 flex items-center">Status:</span>
              {['PENDING', 'APPROVED', 'REJECTED', 'PROCESSING'].map((status) => (
                <Button
                  key={status}
                  variant={selectedStatus === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus(status)}
                  className={selectedStatus === status ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                >
                  {status}
                </Button>
              ))}
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
            </div>
          ) : filteredPayments.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-400 text-lg mb-2">
                No {selectedStatus.toLowerCase()} payments found
              </p>
              {searchQuery && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSearchQuery('')}
                  className="mt-4"
                >
                  Clear Search
                </Button>
              )}
            </div>
          ) : (
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-700">
                <p className="text-sm text-gray-400">
                  Showing {filteredPayments.length} of {payments.length} payments
                </p>
              </div>

              <div className="divide-y divide-gray-700">
                {filteredPayments.map((payment) => (
                  <div key={payment.id} className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">
                          Order #{payment.order.orderNumber}
                        </h3>
                        <p className="text-sm text-gray-400">
                          {payment.order.firstName} {payment.order.lastName} • {payment.order.email}
                        </p>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getStatusBadge(payment.status)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedPayment(payment)}
                        >
                          <EyeIcon className="w-4 h-4 mr-2" />
                          Review
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-400 mb-1">Payment Method</p>
                        <p className="text-white font-medium">{payment.paymentMethod}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400 mb-1">Amount</p>
                        <p className="text-white font-medium">
                          {formatCurrency(payment.amount)} {payment.currency}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400 mb-1">Submitted</p>
                        <p className="text-white font-medium">
                          {new Date(payment.submittedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {payment.transactionHash && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-400 mb-1">Transaction Hash</p>
                        <p className="text-white font-mono text-sm break-all">
                          {payment.transactionHash}
                        </p>
                      </div>
                    )}

                    {payment.status === 'PENDING' && (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewPayment(payment.id, 'REJECTED')}
                          className="border-red-600 text-red-400 hover:bg-red-900/20"
                        >
                          <XCircleIcon className="w-4 h-4 mr-2" />
                          Reject
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewPayment(payment.id, 'APPROVED')}
                          className="border-green-600 text-green-400 hover:bg-green-900/20"
                        >
                          <CheckCircleIcon className="w-4 h-4 mr-2" />
                          Approve
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <PaymentMethodsSettings />
      )}
    </div>
  )
}
