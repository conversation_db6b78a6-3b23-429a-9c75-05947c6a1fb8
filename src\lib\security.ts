import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Simple HTML sanitization without external dependency
function stripHtml(html: string): string {
  return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
             .replace(/<[^>]*>/g, '')
             .replace(/javascript:/gi, '')
             .replace(/on\w+\s*=/gi, '')
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface RateLimitOptions {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string
}

export function rateLimit(options: RateLimitOptions) {
  return async (request: NextRequest): Promise<{ success: boolean; error?: string }> => {
    const key = options.keyGenerator ? options.keyGenerator(request) : getClientIP(request)
    const now = Date.now()
    
    // Clean up expired entries
    for (const [k, v] of rateLimitStore.entries()) {
      if (v.resetTime < now) {
        rateLimitStore.delete(k)
      }
    }
    
    const record = rateLimitStore.get(key)
    
    if (!record) {
      rateLimitStore.set(key, {
        count: 1,
        resetTime: now + options.windowMs
      })
      return { success: true }
    }
    
    if (record.resetTime < now) {
      // Reset window
      record.count = 1
      record.resetTime = now + options.windowMs
      return { success: true }
    }
    
    if (record.count >= options.maxRequests) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${Math.ceil((record.resetTime - now) / 1000)} seconds.`
      }
    }
    
    record.count++
    return { success: true }
  }
}

export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

// Input sanitization
export function sanitizeHtml(html: string): string {
  // Basic HTML sanitization - remove dangerous elements and attributes
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // Remove iframe tags
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '') // Remove object tags
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '') // Remove embed tags
    .replace(/javascript:/gi, 'about:blank#') // Replace javascript: URLs
    .replace(/on\w+\s*=/gi, 'data-removed=') // Remove event handlers
    .replace(/style\s*=\s*["'][^"']*expression\s*\([^"']*["']/gi, '') // Remove CSS expressions
    .replace(/style\s*=\s*["'][^"']*javascript\s*:[^"']*["']/gi, '') // Remove javascript in styles
}

export function sanitizeString(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

// File validation
export function validateFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = filename.toLowerCase().split('.').pop()
  return extension ? allowedTypes.includes(`.${extension}`) : false
}

export function validateFileSize(size: number, maxSize: number): boolean {
  return size <= maxSize
}

// Authorization helpers
export async function canAccessFile(userId: string, fileKey: string): Promise<boolean> {
  try {
    // Check if user has purchased a product that contains this file
    const purchase = await prisma.purchase.findFirst({
      where: {
        userId,
        product: {
          OR: [
            { fileKey },
            { downloadFiles: { some: { fileUrl: { contains: fileKey } } } }
          ]
        }
      }
    })
    
    return !!purchase
  } catch (error) {
    console.error('Error checking file access:', error)
    return false
  }
}

export async function canAccessOrder(userId: string, orderId: string): Promise<boolean> {
  try {
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId
      }
    })
    
    return !!order
  } catch (error) {
    console.error('Error checking order access:', error)
    return false
  }
}

// Password validation
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  // Check for common weak passwords
  const commonPasswords = [
    'password', '123456789', 'qwerty123', 'admin123', 'password1',
    'welcome123', 'letmein123', 'monkey123', '1234567890', '12345678'
  ]

  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('Password is too common. Please choose a more secure password')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// CSRF protection
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  // In a real implementation, you'd store and validate CSRF tokens properly
  // This is a simplified version
  return token && token.length > 10
}

// SQL injection prevention helpers
export function escapeSQL(input: string): string {
  return input.replace(/'/g, "''").replace(/;/g, '\\;')
}

// Security headers
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';"
}

// Error handling that doesn't leak sensitive information
export function createSafeErrorResponse(error: any, isDevelopment: boolean = false) {
  if (isDevelopment) {
    return {
      success: false,
      message: error.message || 'An error occurred',
      stack: error.stack,
      details: error
    }
  }
  
  // Production error response - don't leak sensitive info
  const safeErrors = [
    'Validation error',
    'Authentication required',
    'Access denied',
    'Resource not found',
    'Invalid input',
    'Rate limit exceeded'
  ]
  
  const message = safeErrors.includes(error.message) 
    ? error.message 
    : 'An unexpected error occurred'
  
  return {
    success: false,
    message,
    code: error.code || 'INTERNAL_ERROR'
  }
}
