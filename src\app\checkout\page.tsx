'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useCartStore } from '@/store/cart-store'
import { Button } from '@/components/ui/button'
import { StripePaymentForm } from '@/components/payments/stripe-payment-form'
import { PayPalPaymentForm } from '@/components/payments/paypal-payment-form'
import { ManualPaymentForm } from '@/components/payments/manual-payment-form'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import {
  ShoppingBagIcon,
  ArrowLeftIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export default function CheckoutPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { items, getTotalPrice, clearCart, isHydrated } = useCartStore()
  const [isProcessing, setIsProcessing] = useState(false)
  const [clientSecret, setClientSecret] = useState<string | null>(null)
  const [orderId, setOrderId] = useState<string | null>(null)

  const [formData, setFormData] = useState({
    email: session?.user?.email || '',
    firstName: '',
    lastName: '',
    paymentMethod: 'stripe',
    acceptTerms: false
  })
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null)
  const [couponError, setCouponError] = useState('')
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'stripe' | 'paypal' | 'manual'>('stripe')
  const [paymentMethods, setPaymentMethods] = useState<any[]>([])
  const [selectedManualMethod, setSelectedManualMethod] = useState<any>(null)

  // Calculate pricing for product purchases
  const subtotalPrice = isHydrated ? getTotalPrice() : 0
  const couponDiscountAmount = appliedCoupon?.discountAmount || 0
  const manualPaymentDiscount = selectedPaymentMethod === 'manual' && selectedManualMethod
    ? (subtotalPrice * (selectedManualMethod.discountPercentage / 100))
    : 0
  const totalDiscountAmount = couponDiscountAmount + manualPaymentDiscount
  const totalPrice = Math.max(0, subtotalPrice - totalDiscountAmount)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/checkout')
    }
  }, [status, router])

  // Handle URL parameters for payment status
  useEffect(() => {
    const error = searchParams.get('error')
    const message = searchParams.get('message')
    const cancelled = searchParams.get('cancelled')

    if (error) {
      let errorMessage = 'Payment failed. Please try again.'

      switch (error) {
        case 'missing_order_id':
          errorMessage = 'Payment session expired. Please try again.'
          break
        case 'order_not_found':
          errorMessage = 'Order not found. Please start a new checkout.'
          break
        case 'capture_failed':
          errorMessage = message ? decodeURIComponent(message) : 'Payment capture failed. Please contact support.'
          break
        case 'unexpected_error':
          errorMessage = 'An unexpected error occurred. Please try again.'
          break
      }

      toast.error(errorMessage)

      // Clean up URL parameters
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete('error')
      newUrl.searchParams.delete('message')
      window.history.replaceState({}, '', newUrl.toString())
    }

    if (cancelled === 'true') {
      const cancelMessage = message ? decodeURIComponent(message) : 'Payment was cancelled.'
      toast.error(cancelMessage)

      // Clean up URL parameters
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete('cancelled')
      newUrl.searchParams.delete('message')
      window.history.replaceState({}, '', newUrl.toString())
    }
  }, [searchParams])

  // Update form data when session loads
  useEffect(() => {
    // Redirect membership purchases to new subscription system
    const checkoutType = searchParams.get('type')
    if (checkoutType === 'membership') {
      router.push('/subscription')
      return
    }

    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        email: session.user.email || '',
        firstName: '',
        lastName: ''
      }))
    }
  }, [session, searchParams, router])

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        const response = await fetch('/api/payment-methods')
        const data = await response.json()
        if (data.success) {
          setPaymentMethods(data.data)
          // Set default manual method to USDT if available
          const usdtMethod = data.data.find((method: any) => method.currency === 'USDT')
          if (usdtMethod) {
            setSelectedManualMethod(usdtMethod)
          }
        }
      } catch (error) {
        console.error('Error fetching payment methods:', error)
      }
    }

    fetchPaymentMethods()
  }, [])

  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        email: session.user.email || '',
        firstName: session.user.name?.split(' ')[0] || '',
        lastName: session.user.name?.split(' ').slice(1).join(' ') || ''
      }))
    }
  }, [session])

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError('Please enter a coupon code')
      return
    }

    if (!session?.user?.id) {
      setCouponError('Please sign in to use coupons')
      return
    }

    setIsValidatingCoupon(true)
    setCouponError('')

    try {
      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: couponCode.trim(),
          subtotal: subtotalPrice,
          userId: session?.user?.id
        })
      })

      const data = await response.json()

      if (data.success) {
        setAppliedCoupon(data.data)
        toast.success(`Coupon applied! You saved ${formatCurrency(data.data.discountAmount)}`)
      } else {
        setCouponError(data.message)
        setAppliedCoupon(null)
      }
    } catch (error) {
      console.error('Error validating coupon:', error)
      setCouponError('Failed to validate coupon')
      setAppliedCoupon(null)
    } finally {
      setIsValidatingCoupon(false)
    }
  }

  const removeCoupon = () => {
    setCouponCode('')
    setAppliedCoupon(null)
    setCouponError('')
    toast.success('Coupon removed')
  }

  const createPaymentIntent = async () => {
    console.log('createPaymentIntent called')

    if (!formData.acceptTerms) {
      console.log('Terms not accepted')
      toast.error('Please accept the terms and conditions')
      return
    }

    if (items.length === 0) {
      console.log('Cart is empty')
      toast.error('Your cart is empty')
      return
    }

    console.log('Starting payment intent creation...')
    setIsProcessing(true)

    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: items.map(item => ({
            productId: item.product.id,
            quantity: item.quantity
          })),
          customerInfo: {
            email: formData.email,
            firstName: formData.firstName,
            lastName: formData.lastName
          },
          couponCode: appliedCoupon?.coupon?.code,
          paymentMethod: selectedPaymentMethod,
          manualPaymentDiscount: selectedPaymentMethod === 'manual' && selectedManualMethod
            ? selectedManualMethod.discountPercentage
            : 0
        })
      })

      const data = await response.json()

      if (data.success) {
        if (selectedPaymentMethod === 'stripe') {
          setClientSecret(data.data.clientSecret)
        }
        setOrderId(data.data.orderId)
      } else {
        toast.error(data.message || 'Failed to create payment intent')
      }
    } catch (error) {
      console.error('Error creating payment intent:', error)
      toast.error('Failed to initialize payment')
    } finally {
      setIsProcessing(false)
    }
  }

  const handlePaymentSuccess = () => {
    clearCart()
    toast.success('Payment successful!')
    router.push(`/orders/success?orderId=${orderId}`)
  }

  const handleContinueToPayment = () => {
    if (!selectedPaymentMethod) return

    // For now, just scroll to payment section or show payment form
    // This could be enhanced to show a payment modal or navigate to payment step
    const paymentSection = document.getElementById('payment-section')
    if (paymentSection) {
      paymentSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handlePaymentError = (error: string) => {
    toast.error(error)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted, creating payment intent...')
    console.log('Form data:', formData)
    console.log('Selected payment method:', selectedPaymentMethod)
    console.log('Items:', items)
    await createPaymentIntent()
  }

  if (status === 'loading' || !isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  // Show empty cart if cart is empty
  if (isHydrated && items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ShoppingBagIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">Your cart is empty</h2>
          <p className="text-gray-400 mb-8">Add some products to your cart to continue</p>
          <Link href="/products">
            <Button variant="premium">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/cart"
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Cart
          </Link>
          <h1 className="text-3xl font-bold text-white">Checkout</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
              <h2 className="text-xl font-semibold text-white mb-6">Payment Information</h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Customer Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-300 mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-300 mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    required
                  />
                </div>

                {/* Payment Method Selection */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-4">Payment Method</h3>

                  <div className="space-y-4">
                    {/* Credit/Debit Card */}
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                        selectedPaymentMethod === 'stripe'
                          ? 'border-yellow-400 bg-yellow-400/5'
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedPaymentMethod('stripe')}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                            selectedPaymentMethod === 'stripe'
                              ? 'border-yellow-400 bg-yellow-400'
                              : 'border-gray-500'
                          }`}>
                            {selectedPaymentMethod === 'stripe' && (
                              <div className="w-2 h-2 bg-black rounded-full"></div>
                            )}
                          </div>
                          <div>
                            <h4 className="font-semibold text-white">Credit or Debit Card</h4>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* PayPal */}
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                        selectedPaymentMethod === 'paypal'
                          ? 'border-yellow-400 bg-yellow-400/5'
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedPaymentMethod('paypal')}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                            selectedPaymentMethod === 'paypal'
                              ? 'border-yellow-400 bg-yellow-400'
                              : 'border-gray-500'
                          }`}>
                            {selectedPaymentMethod === 'paypal' && (
                              <div className="w-2 h-2 bg-black rounded-full"></div>
                            )}
                          </div>
                          <div>
                            <h4 className="font-semibold text-white">PayPal</h4>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Manual Payment */}
                    {paymentMethods.length > 0 && (
                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                          selectedPaymentMethod === 'manual'
                            ? 'border-yellow-400 bg-yellow-400/5'
                            : 'border-gray-600 hover:border-gray-500'
                        }`}
                        onClick={() => setSelectedPaymentMethod('manual')}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                              selectedPaymentMethod === 'manual'
                                ? 'border-yellow-400 bg-yellow-400'
                                : 'border-gray-500'
                            }`}>
                              {selectedPaymentMethod === 'manual' && (
                                <div className="w-2 h-2 bg-black rounded-full"></div>
                              )}
                            </div>
                            <div>
                              <h4 className="font-semibold text-white">
                                {selectedManualMethod?.currency || 'USDT'} Payment
                              </h4>
                              {selectedManualMethod?.discountPercentage > 0 && (
                                <p className="text-sm text-green-400">
                                  {selectedManualMethod.discountPercentage}% discount applied
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <div className="w-10 h-6 bg-orange-500 rounded flex items-center justify-center">
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={(e) => setFormData(prev => ({ ...prev, acceptTerms: e.target.checked }))}
                    className="mt-1 w-4 h-4 text-yellow-400 bg-gray-800 border-gray-600 rounded focus:ring-yellow-400 focus:ring-2"
                    required
                  />
                  <label htmlFor="acceptTerms" className="text-sm text-gray-300">
                    I agree to the{' '}
                    <span className="text-yellow-400 hover:text-yellow-300 cursor-pointer">
                      Terms of Service
                    </span>
                    {' '}and{' '}
                    <span className="text-yellow-400 hover:text-yellow-300 cursor-pointer">
                      Privacy Policy
                    </span>
                  </label>
                </div>

                {/* Payment Forms */}
                {selectedPaymentMethod === 'stripe' && (
                  clientSecret ? (
                    <StripePaymentForm
                      clientSecret={clientSecret}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  ) : (
                    <Button
                      type="submit"
                      variant="premium"
                      className="w-full"
                      disabled={isProcessing || !formData.acceptTerms}
                    >
                      {isProcessing ? (
                        <>
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <LockClosedIcon className="w-5 h-5 mr-2" />
                          Proceed to Payment - {formatCurrency(totalPrice)}
                        </>
                      )}
                    </Button>
                  )
                )}

                {selectedPaymentMethod === 'paypal' && (
                  <PayPalPaymentForm
                    amount={totalPrice}
                    items={items.map(item => ({
                      productId: item.product.id,
                      quantity: item.quantity
                    }))}
                    customerInfo={{
                      email: formData.email,
                      firstName: formData.firstName,
                      lastName: formData.lastName
                    }}
                    couponCode={appliedCoupon?.coupon?.code}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                )}

                {selectedPaymentMethod === 'manual' && selectedManualMethod && (
                  <ManualPaymentForm
                    amount={totalPrice}
                    paymentMethod={selectedManualMethod}
                    items={items.map(item => ({
                      productId: item.product.id,
                      quantity: item.quantity
                    }))}
                    customerInfo={{
                      email: formData.email,
                      firstName: formData.firstName,
                      lastName: formData.lastName
                    }}
                    couponCode={appliedCoupon?.coupon?.code}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                  />
                )}
              </form>
            </div>
          </div>

          {/* Order Summary */}
          <div>
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-white mb-6">Order Summary</h2>

              <div className="space-y-4 mb-6">
                {items.map((item) => {
                  const effectivePrice = item.product.isOnSale && item.product.salePrice
                    ? item.product.salePrice
                    : item.product.price

                  return (
                    <div key={item.id} className="flex items-center space-x-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-gray-700 rounded-lg overflow-hidden">
                        {item.product.images && item.product.images.length > 0 ? (
                          <Image
                            src={item.product.images[0] || '/placeholder-product.jpg'}
                            alt={item.product.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <ShoppingBagIcon className="w-6 h-6 text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-medium text-sm line-clamp-2">
                          {item.product.name}
                        </h3>
                        <p className="text-gray-400 text-xs">
                          Qty: {item.quantity} × {formatCurrency(Number(effectivePrice))}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Coupon Section */}
              <div className="border-t border-gray-700 pt-4 mb-4">
                <h3 className="text-white font-medium mb-3">Coupon Code</h3>
                {!appliedCoupon ? (
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        placeholder="Enter coupon code"
                        className="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-sm"
                      />
                      <Button
                        type="button"
                        onClick={validateCoupon}
                        disabled={isValidatingCoupon || !couponCode.trim()}
                        className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-lg transition-colors duration-200 text-sm"
                      >
                        {isValidatingCoupon ? (
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          'Apply'
                        )}
                      </Button>
                    </div>
                    {couponError && (
                      <p className="text-red-400 text-xs">{couponError}</p>
                    )}
                  </div>
                ) : (
                  <div className="bg-green-900/20 border border-green-700/50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-400 font-medium text-sm">
                          {appliedCoupon.coupon.code}
                        </p>
                        <p className="text-green-300 text-xs">
                          {appliedCoupon.coupon.name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-green-400 font-medium text-sm">
                          -{formatCurrency(appliedCoupon.discountAmount)}
                        </span>
                        <button
                          onClick={removeCoupon}
                          className="text-gray-400 hover:text-white transition-colors duration-200"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Order Total */}
              <div className="border-t border-gray-700 pt-4">
                <div className="space-y-2 mb-3">
                  <div className="flex items-center justify-between text-gray-300">
                    <span>Subtotal</span>
                    <span>{formatCurrency(subtotalPrice)}</span>
                  </div>
                  {appliedCoupon && (
                    <div className="flex items-center justify-between text-green-400">
                      <span>Coupon Discount ({appliedCoupon.coupon.code})</span>
                      <span>-{formatCurrency(couponDiscountAmount)}</span>
                    </div>
                  )}
                  {manualPaymentDiscount > 0 && (
                    <div className="flex items-center justify-between text-orange-400">
                      <span>Payment Method Discount ({selectedManualMethod?.discountPercentage}%)</span>
                      <span>-{formatCurrency(manualPaymentDiscount)}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between text-lg font-semibold text-white border-t border-gray-700 pt-3">
                  <span>Total</span>
                  <span className="text-yellow-400">{formatCurrency(totalPrice)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
