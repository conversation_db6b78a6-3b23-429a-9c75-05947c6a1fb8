import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getUserMembership, checkDownloadLimit, getMembershipPlan } from '@/lib/membership'

// GET /api/memberships/status - Get user's membership status
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { status: 401 })
    }

    // Get user's membership
    const membership = await getUserMembership(session.user.id)
    
    if (!membership) {
      return NextResponse.json({
        success: true,
        hasMembership: false,
        membership: null,
        downloadLimits: null
      })
    }

    // Get membership plan details
    const plan = getMembershipPlan(membership.type)
    
    // Get download limits
    const downloadLimits = await checkDownloadLimit(session.user.id)

    return NextResponse.json({
      success: true,
      hasMembership: true,
      membership: {
        id: membership.id,
        type: membership.type,
        status: membership.status,
        startDate: membership.startDate,
        endDate: membership.endDate,
        autoRenew: membership.autoRenew,
        plan: plan
      },
      downloadLimits: {
        canDownload: downloadLimits.canDownload,
        remainingDownloads: downloadLimits.remainingDownloads,
        dailyLimit: downloadLimits.dailyLimit,
        resetTime: downloadLimits.resetTime
      }
    })

  } catch (error) {
    console.error('Error getting membership status:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to get membership status'
    }, { status: 500 })
  }
}
