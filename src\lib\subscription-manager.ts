import { prisma } from '@/lib/prisma'
import { getPayPalSubscription, mapPayPalSubscriptionStatus } from '@/lib/paypal-subscriptions'

export interface SubscriptionPlan {
  type: 'BRONZE' | 'SILVER' | 'GOLD'
  name: string
  price: number
  dailyDownloads: number
  features: string[]
  paypalPlanId: string
}

export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  BRONZE: {
    type: 'BRONZE',
    name: 'Bronze Plan',
    price: 29.99,
    dailyDownloads: 10,
    features: [
      'Download our all tools become VIP',
      '1 Month unlimited download',
      'Real value over $ 3000+',
      '500+ working forex robot and indicators',
      'Daily update new tools',
      'Includes ALL future products and updates',
      'VIP Telegram Support Group'
    ],
    paypalPlanId: process.env.PAYPAL_BRONZE_PLAN_ID || 'P-5ML4271244454362WXNWU5NQ'
  },
  SILVER: {
    type: 'SILVE<PERSON>',
    name: 'Silver Plan',
    price: 39.99,
    dailyDownloads: 25,
    features: [
      'Everything in Bronze Plan',
      '3 Months unlimited download',
      'Priority customer support',
      'Early access to new tools',
      'Advanced trading strategies',
      'Custom indicator requests',
      'VIP Telegram Support Group'
    ],
    paypalPlanId: process.env.PAYPAL_SILVER_PLAN_ID || 'P-1GJ4271244454362WXNWU5NR'
  },
  GOLD: {
    type: 'GOLD',
    name: 'Gold Plan',
    price: 49.99,
    dailyDownloads: 100,
    features: [
      'Everything in Silver Plan',
      '12 Months unlimited download',
      'Dedicated account manager',
      '1-on-1 trading consultation',
      'Custom EA development',
      'Backtesting services',
      'VIP Telegram Support Group'
    ],
    paypalPlanId: process.env.PAYPAL_GOLD_PLAN_ID || 'P-8HL4271244454362WXNWU5NS'
  }
}

/**
 * Get subscription plan details
 */
export function getSubscriptionPlan(type: 'BRONZE' | 'SILVER' | 'GOLD'): SubscriptionPlan {
  return SUBSCRIPTION_PLANS[type]
}

/**
 * Get user's active subscription
 */
export async function getUserSubscription(userId: string) {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { userId },
      include: {
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 5
        }
      }
    })

    if (!subscription) {
      return null
    }

    // Check if subscription is still active
    const now = new Date()
    const isActive = subscription.status === 'ACTIVE' && 
                    subscription.currentPeriodEnd > now &&
                    !subscription.cancelAtPeriodEnd

    return {
      ...subscription,
      isActive,
      plan: getSubscriptionPlan(subscription.planType)
    }
  } catch (error) {
    console.error('Error getting user subscription:', error)
    return null
  }
}

/**
 * Create a new subscription record
 */
export async function createSubscription(
  userId: string,
  planType: 'BRONZE' | 'SILVER' | 'GOLD',
  paypalSubscriptionId: string,
  currentPeriodStart: Date,
  currentPeriodEnd: Date
) {
  try {
    return await prisma.subscription.create({
      data: {
        userId,
        planType,
        status: 'PENDING',
        paypalSubscriptionId,
        currentPeriodStart,
        currentPeriodEnd,
        nextBillingDate: currentPeriodEnd,
        billingCycleSequence: 1
      }
    })
  } catch (error) {
    console.error('Error creating subscription:', error)
    throw error
  }
}

/**
 * Update subscription status from PayPal webhook
 */
export async function updateSubscriptionFromPayPal(paypalSubscriptionId: string) {
  try {
    // Get latest subscription data from PayPal
    const paypalSubscription = await getPayPalSubscription(paypalSubscriptionId)
    
    // Find our subscription record
    const subscription = await prisma.subscription.findFirst({
      where: { paypalSubscriptionId }
    })

    if (!subscription) {
      console.error('Subscription not found for PayPal ID:', paypalSubscriptionId)
      return null
    }

    // Map PayPal status to our status
    const status = mapPayPalSubscriptionStatus(paypalSubscription.status)
    
    // Calculate period dates from PayPal data
    const currentPeriodStart = new Date(paypalSubscription.start_time)
    const nextBillingTime = paypalSubscription.billing_info?.next_billing_time
    const currentPeriodEnd = nextBillingTime ? new Date(nextBillingTime) : subscription.currentPeriodEnd

    // Update subscription
    return await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status,
        currentPeriodStart,
        currentPeriodEnd,
        nextBillingDate: nextBillingTime ? new Date(nextBillingTime) : null,
        metadata: {
          paypalData: paypalSubscription,
          lastUpdated: new Date().toISOString()
        },
        updatedAt: new Date()
      }
    })
  } catch (error) {
    console.error('Error updating subscription from PayPal:', error)
    throw error
  }
}

/**
 * Cancel subscription (at end of current period)
 */
export async function cancelSubscription(userId: string, reason?: string) {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { userId }
    })

    if (!subscription) {
      throw new Error('No active subscription found')
    }

    if (subscription.status !== 'ACTIVE') {
      throw new Error('Subscription is not active')
    }

    // Update our record to cancel at period end
    return await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        cancelAtPeriodEnd: true,
        cancelledAt: new Date(),
        metadata: {
          ...subscription.metadata as any,
          cancellationReason: reason || 'User requested cancellation',
          cancelledAt: new Date().toISOString()
        },
        updatedAt: new Date()
      }
    })
  } catch (error) {
    console.error('Error cancelling subscription:', error)
    throw error
  }
}

/**
 * Check user's daily download limits
 */
export async function checkSubscriptionDownloadLimit(userId: string): Promise<{
  canDownload: boolean
  remainingDownloads: number
  dailyLimit: number
  planType: string | null
  resetTime: Date
}> {
  try {
    // Get user's active subscription
    const subscription = await getUserSubscription(userId)
    
    if (!subscription || !subscription.isActive) {
      return {
        canDownload: false,
        remainingDownloads: 0,
        dailyLimit: 0,
        planType: null,
        resetTime: new Date()
      }
    }

    const plan = subscription.plan
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Get or create today's download limit record
    const dailyLimit = await prisma.dailyDownloadLimit.upsert({
      where: {
        userId_date: {
          userId,
          date: today
        }
      },
      update: {},
      create: {
        userId,
        date: today,
        downloadCount: 0,
        membershipType: subscription.planType,
        dailyLimit: plan.dailyDownloads
      }
    })

    const remainingDownloads = Math.max(0, plan.dailyDownloads - dailyLimit.downloadCount)
    const canDownload = remainingDownloads > 0

    // Calculate reset time (next day at midnight)
    const resetTime = new Date(today)
    resetTime.setDate(resetTime.getDate() + 1)

    return {
      canDownload,
      remainingDownloads,
      dailyLimit: plan.dailyDownloads,
      planType: subscription.planType,
      resetTime
    }
  } catch (error) {
    console.error('Error checking download limit:', error)
    return {
      canDownload: false,
      remainingDownloads: 0,
      dailyLimit: 0,
      planType: null,
      resetTime: new Date()
    }
  }
}

/**
 * Record a download for subscription user
 */
export async function recordSubscriptionDownload(userId: string, productId: string) {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Increment download count
    await prisma.dailyDownloadLimit.upsert({
      where: {
        userId_date: {
          userId,
          date: today
        }
      },
      update: {
        downloadCount: {
          increment: 1
        },
        updatedAt: new Date()
      },
      create: {
        userId,
        date: today,
        downloadCount: 1,
        membershipType: 'BRONZE', // Will be updated by checkSubscriptionDownloadLimit
        dailyLimit: 10 // Will be updated by checkSubscriptionDownloadLimit
      }
    })

    console.log('Download recorded for subscription user:', { userId, productId, date: today })
  } catch (error) {
    console.error('Error recording subscription download:', error)
    throw error
  }
}

/**
 * Get subscription analytics for admin
 */
export async function getSubscriptionAnalytics() {
  try {
    const [
      totalSubscriptions,
      activeSubscriptions,
      cancelledSubscriptions,
      subscriptionsByPlan,
      recentPayments
    ] = await Promise.all([
      prisma.subscription.count(),
      prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          currentPeriodEnd: { gt: new Date() }
        }
      }),
      prisma.subscription.count({
        where: {
          OR: [
            { status: 'CANCELLED' },
            { cancelAtPeriodEnd: true }
          ]
        }
      }),
      prisma.subscription.groupBy({
        by: ['planType'],
        _count: { planType: true },
        where: {
          status: 'ACTIVE'
        }
      }),
      prisma.subscriptionPayment.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          subscription: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          }
        }
      })
    ])

    return {
      totalSubscriptions,
      activeSubscriptions,
      cancelledSubscriptions,
      subscriptionsByPlan: subscriptionsByPlan.reduce((acc, item) => {
        acc[item.planType] = item._count.planType
        return acc
      }, {} as Record<string, number>),
      recentPayments
    }
  } catch (error) {
    console.error('Error getting subscription analytics:', error)
    throw error
  }
}
