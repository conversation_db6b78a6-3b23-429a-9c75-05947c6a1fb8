'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/store/cart-store'
import { useWishlistStore } from '@/store/wishlist-store'
import { ProductWithRelations } from '@/types'
import { formatCurrency } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import {
  HeartIcon,
  ShoppingCartIcon,
  StarIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import toast from 'react-hot-toast'

interface ProductCardProps {
  product: ProductWithRelations
  className?: string
}

export function ProductCard({ product, className = '' }: ProductCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isInCart, setIsInCart] = useState(false)
  const { data: session } = useSession()
  const { addItem, hasItem, openCart, isHydrated } = useCartStore()
  const { hasItem: isWishlisted, toggleItem: toggleWishlist, isHydrated: wishlistHydrated } = useWishlistStore()

  const effectivePrice = product.isOnSale && product.salePrice
    ? product.salePrice
    : product.price

  const discountPercentage = product.isOnSale && product.originalPrice && product.salePrice
    ? Math.round(((product.originalPrice - product.salePrice) / product.originalPrice) * 100)
    : 0

  // Update cart state after hydration
  useEffect(() => {
    if (isHydrated) {
      setIsInCart(hasItem(product.id))
    }
  }, [isHydrated, hasItem, product.id])

  // Subscribe to cart changes after hydration
  useEffect(() => {
    if (!isHydrated) return

    const unsubscribe = useCartStore.subscribe((state) => {
      setIsInCart(state.hasItem(product.id))
    })

    return unsubscribe
  }, [isHydrated, product.id])

  const handleAddToCart = async () => {
    setIsLoading(true)
    try {
      addItem(product)
      toast.success('Added to cart!')
      openCart()
    } catch (error) {
      toast.error('Failed to add to cart')
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleWishlist = async () => {
    if (!session) {
      toast.error('Please sign in to add items to wishlist')
      return
    }
    await toggleWishlist(product.id)
  }

  return (
    <div className={`group relative bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden hover:border-yellow-500/50 transition-all duration-300 hover:shadow-2xl hover:shadow-yellow-500/10 flex flex-col h-full w-full ${className}`}>
      {/* Sale Badge */}
      {product.isOnSale && discountPercentage > 0 && (
        <div className="absolute top-3 left-3 z-10 bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-semibold shadow-lg">
          -{discountPercentage}%
        </div>
      )}

      {/* Featured Badge */}
      {product.featured && (
        <div className="absolute top-3 right-3 z-10 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-semibold shadow-lg">
          Featured
        </div>
      )}

      {/* Product Image */}
      <div className="relative aspect-[4/3] overflow-hidden bg-gray-800 flex-shrink-0">
        {product.images && product.images.length > 0 ? (
          <Image
            src={product.images[0]}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700 to-gray-800">
            <ArrowDownTrayIcon className="w-16 h-16 text-gray-500" />
          </div>
        )}

        {/* Overlay Actions */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
          <Link href={`/products/${product.slug}`}>
            <Button variant="glass" size="sm">
              <EyeIcon className="w-4 h-4 mr-2" />
              View
            </Button>
          </Link>
          <Button
            variant="glass"
            size="sm"
            onClick={handleToggleWishlist}
          >
            {isWishlisted ? (
              <HeartSolidIcon className="w-4 h-4 text-red-500" />
            ) : (
              <HeartIcon className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-5 flex flex-col flex-grow">
        {/* Category and Rating */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-xs text-yellow-400 font-medium uppercase tracking-wide truncate bg-yellow-400/10 px-2 py-1 rounded-full">
            {product.category.name}
          </span>

          {/* Rating */}
          <div className="flex items-center space-x-1 flex-shrink-0">
            <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="text-sm text-gray-300 font-medium">
              {product.averageRating?.toFixed(1) || '0.0'}
            </span>
            <span className="text-xs text-gray-500">
              ({product._count?.reviews || 0})
            </span>
          </div>
        </div>

        {/* Product Name */}
        <Link href={`/products/${product.slug}`}>
          <h3 className="text-base font-semibold text-white mb-3 line-clamp-2 hover:text-yellow-400 transition-colors duration-200 min-h-[3rem] leading-tight">
            {product.name}
          </h3>
        </Link>

        {/* Description */}
        {product.shortDescription && (
          <p className="text-gray-400 text-sm mb-4 line-clamp-3 leading-relaxed">
            {product.shortDescription}
          </p>
        )}

        {/* Tags */}
        {product.tags && product.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {product.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-800/50 text-gray-300 text-xs rounded-md border border-gray-700/50 truncate max-w-[100px]"
                title={tag}
              >
                {tag}
              </span>
            ))}
            {product.tags.length > 3 && (
              <span className="px-2 py-1 bg-gray-800/50 text-gray-300 text-xs rounded-md border border-gray-700/50">
                +{product.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Spacer to push price/actions to bottom */}
        <div className="flex-grow"></div>

        {/* Price and Actions */}
        <div className="space-y-3 mt-auto">
          {/* Price */}
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <span className="text-xl font-bold text-white">
                {formatCurrency(effectivePrice)}
              </span>
              {product.isOnSale && product.originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  {formatCurrency(product.originalPrice)}
                </span>
              )}
            </div>

            {/* Downloads count */}
            <div className="text-right">
              <div className="text-xs text-gray-500">Downloads</div>
              <div className="text-sm text-gray-300 font-medium">
                {product._count?.downloads || 0}
              </div>
            </div>
          </div>

          {/* Action Button */}
          <Button
            onClick={handleAddToCart}
            disabled={isLoading || (isHydrated && isInCart)}
            variant={isHydrated && isInCart ? "secondary" : "premium"}
            size="sm"
            className="w-full text-sm font-medium"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : isHydrated && isInCart ? (
              <>
                <ShoppingCartIcon className="w-4 h-4 mr-2" />
                In Cart
              </>
            ) : (
              <>
                <ShoppingCartIcon className="w-4 h-4 mr-2" />
                Add to Cart
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Premium Shine Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 pointer-events-none" />
    </div>
  )
}
