import { NextResponse } from 'next/server'
import { getPayPalClientId } from '@/lib/paypal'
import { securityHeaders } from '@/lib/security'

export async function GET() {
  try {
    const clientId = getPayPalClientId()
    
    return NextResponse.json({
      success: true,
      clientId
    }, {
      headers: securityHeaders
    })
  } catch (error) {
    console.error('Error getting PayPal config:', error)
    
    return NextResponse.json({
      success: false,
      message: 'PayPal not configured'
    }, {
      status: 500,
      headers: securityHeaders
    })
  }
}
